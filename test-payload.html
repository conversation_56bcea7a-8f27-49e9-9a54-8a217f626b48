<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XSS Payload Test</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: #1a1a1a;
            color: #00ff00;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: #000;
            border: 2px solid #00ff00;
            border-radius: 10px;
            padding: 20px;
        }
        
        .payload-input {
            width: 100%;
            height: 100px;
            background: #1a1a1a;
            color: #00ff00;
            border: 2px solid #333;
            border-radius: 5px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
        
        .test-btn {
            background: #00ff00;
            color: #000;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            margin: 10px 5px;
        }
        
        .result {
            background: #333;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #00ff00;
        }
        
        .success {
            border-left-color: #00ff00;
            background: rgba(0, 255, 0, 0.1);
        }
        
        .error {
            border-left-color: #ff0000;
            background: rgba(255, 0, 0, 0.1);
            color: #ff6666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 XSS Payload Tester</h1>
        <p>Test your XSS payloads here without initialization errors!</p>
        
        <div>
            <h3>Enter Payload:</h3>
            <textarea class="payload-input" id="payload-input" placeholder="Enter your XSS payload here..."></textarea>
            <br>
            <button class="test-btn" onclick="testPayload()">🚀 Test Payload</button>
            <button class="test-btn" onclick="loadExample()">📝 Load Example</button>
            <button class="test-btn" onclick="clearResults()">🗑️ Clear</button>
        </div>
        
        <div id="results"></div>
        
        <div class="result">
            <h3>🎯 Working Payloads:</h3>
            <p><strong>Ultimate Master Payload:</strong></p>
            <code>&amp;lt;script&amp;gt;alert('Fibonacci:1,1,2,3,5,8,13,21,34 Binary:1111000=120 Caesar:WKLV_LV_D_WHVW=THIS_IS_A_TEST Hex:48656c6c6f=Hello')&amp;lt;/script&amp;gt;</code>
            
            <p><strong>Simple Bypass:</strong></p>
            <code>&amp;lt;script&amp;gt;alert('XSS')&amp;lt;/script&amp;gt;</code>
            
            <p><strong>Image Vector:</strong></p>
            <code>&amp;lt;img src=x onerror=alert('XSS')&amp;gt;</code>
        </div>
    </div>

    <script>
        // Simple XSS filter simulation (without complex dependencies)
        function simpleXSSFilter(input) {
            let result = {
                original: input,
                filtered: input,
                blocked: false,
                warnings: [],
                bypassDetected: false,
                flagRevealed: false
            };
            
            // Basic script filter
            if (/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi.test(input)) {
                result.blocked = true;
                result.warnings.push('Script tags detected and blocked');
                return result;
            }
            
            // Event handler filter
            if (/on\w+\s*=/gi.test(input)) {
                result.blocked = true;
                result.warnings.push('Event handlers detected and blocked');
                return result;
            }
            
            // HTML entity filter (vulnerable to double encoding)
            let decoded = input;
            decoded = decoded.replace(/&lt;/gi, '<');
            decoded = decoded.replace(/&gt;/gi, '>');
            decoded = decoded.replace(/&amp;/gi, '&');
            
            // Check if decoded version contains dangerous patterns
            if (/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi.test(decoded)) {
                result.blocked = true;
                result.warnings.push('Dangerous HTML entities detected after decoding');
                return result;
            }
            
            if (/on\w+\s*=/gi.test(decoded)) {
                result.blocked = true;
                result.warnings.push('Event handlers detected after decoding');
                return result;
            }
            
            // Check for double encoding bypass
            if (/&amp;lt;.*?&amp;gt;/gi.test(input)) {
                result.bypassDetected = true;
                result.filtered = decoded;
                
                // Check for logic puzzles
                const hasFibonacci = /1,1,2,3,5,8,13,21,34/.test(input);
                const hasBinary = /1111000/.test(input);
                const hasCaesar = /WKLV.*?WHVW/.test(input);
                const hasHex = /48656c6c6f/.test(input);
                
                if (hasFibonacci && hasBinary && hasCaesar && hasHex) {
                    result.flagRevealed = true;
                    result.flag = 'WOLF{xss_byp4ss_3xp3rt}';
                }
            }
            
            return result;
        }
        
        function testPayload() {
            const input = document.getElementById('payload-input').value.trim();
            const resultsDiv = document.getElementById('results');
            
            if (!input) {
                showResult('Please enter a payload to test.', 'error');
                return;
            }
            
            const result = simpleXSSFilter(input);
            
            let html = '<div class="result">';
            html += '<h3>🔍 Test Results</h3>';
            html += `<p><strong>Original:</strong> <code>${escapeHtml(result.original)}</code></p>`;
            html += `<p><strong>Status:</strong> ${result.blocked ? '🛡️ BLOCKED' : '✅ PASSED'}</p>`;
            
            if (result.warnings.length > 0) {
                html += '<p><strong>Warnings:</strong></p><ul>';
                result.warnings.forEach(warning => {
                    html += `<li>${warning}</li>`;
                });
                html += '</ul>';
            }
            
            if (result.bypassDetected) {
                html += '<p><strong>🚨 BYPASS DETECTED!</strong></p>';
                html += `<p><strong>Filtered Output:</strong> <code>${escapeHtml(result.filtered)}</code></p>`;
            }
            
            if (result.flagRevealed) {
                html += '<div style="background: linear-gradient(45deg, #28a745, #20c997); color: white; padding: 15px; border-radius: 8px; margin: 10px 0; text-align: center;">';
                html += '<h3>🎉 SUCCESS! 🎉</h3>';
                html += `<p><strong>Flag:</strong> <span style="font-size: 18px; font-family: monospace;">${result.flag}</span></p>';
                html += '<p>Congratulations! You successfully bypassed the XSS filter!</p>';
                html += '</div>';
            }
            
            html += '</div>';
            resultsDiv.innerHTML = html;
        }
        
        function loadExample() {
            const examples = [
                '&amp;lt;script&amp;gt;alert(\'XSS\')&amp;lt;/script&amp;gt;',
                '&amp;lt;img src=x onerror=alert(\'XSS\')&amp;gt;',
                '&amp;lt;script&amp;gt;alert(\'Fibonacci:1,1,2,3,5,8,13,21,34 Binary:1111000=120 Caesar:WKLV_LV_D_WHVW=THIS_IS_A_TEST Hex:48656c6c6f=Hello\')&amp;lt;/script&amp;gt;'
            ];
            
            const randomExample = examples[Math.floor(Math.random() * examples.length)];
            document.getElementById('payload-input').value = randomExample;
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
            document.getElementById('payload-input').value = '';
        }
        
        function showResult(message, type) {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = `<div class="result ${type}"><p>${message}</p></div>`;
        }
        
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
        
        // Initialize
        console.log('XSS Payload Tester loaded successfully!');
        console.log('No initialization errors - ready to test payloads!');
    </script>
</body>
</html>
