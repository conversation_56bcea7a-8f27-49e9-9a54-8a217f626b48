/**
 * Advanced XSS Security Filter System
 * WOLF CTF - Problem 3: XSS Payload Challenge
 * 
 * This filter implements multiple layers of security to prevent XSS attacks.
 * However, it has specific vulnerabilities that can be exploited using advanced techniques.
 */

class AdvancedXSSFilter {
    constructor() {
        this.filterLayers = [
            'basicScriptFilter',
            'eventHandlerFilter',
            'htmlEntityFilter',
            'unicodeNormalization',
            'contextualFilter',
            'advancedPatternFilter',
            'hiddenLogicFilter'
        ];

        this.bypassDetected = false;
        this.flagRevealed = false;
        this.fragmentsUnlocked = new Set();
        this.logicPuzzlesSolved = new Set();
        this.hiddenState = {
            fibonacci_position: 0,
            binary_attempts: 0,
            caesar_attempts: 0,
            hex_attempts: 0,
            steganography_found: false
        };

        // Hidden flag fragments (base64 encoded)
        this.flagFragments = {
            part1: 'V09MRg==',      // WOLF
            part2: 'eHNzX2J5cA==',  // xss_byp
            part3: 'NGFzc18z',      // 4ass_3
            part4: 'eHAzcnQ=',      // xp3rt
            separator: 'e30='       // {}
        };

        // Hidden validation sequences
        this.hiddenSequences = {
            fibonacci: [1, 1, 2, 3, 5, 8, 13, 21, 34, 55, 89],
            primes: [2, 3, 5, 7, 11, 13, 17, 19, 23, 29],
            powers_of_2: [1, 2, 4, 8, 16, 32, 64, 128, 256]
        };

        // Steganographic patterns hidden in comments
        this.hiddenPatterns = {
            morse_code: '... --- ... / .... . .-.. .--.',  // SOS HELP
            binary_message: '01001000 01100101 01101100 01110000', // Help
            hex_clue: '466c61672069732068696464656e', // Flag is hidden
            rot13_hint: 'Synt vf va gur pbqr'  // Flag is in the code
        };

        // Dangerous patterns to block
        this.dangerousPatterns = [
            /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
            /javascript:/gi,
            /on\w+\s*=/gi,
            /<iframe\b[^>]*>/gi,
            /<object\b[^>]*>/gi,
            /<embed\b[^>]*>/gi,
            /<link\b[^>]*>/gi,
            /<meta\b[^>]*>/gi,
            /expression\s*\(/gi,
            /vbscript:/gi,
            /data:text\/html/gi
        ];

        // HTML entities that might be used for bypass
        this.suspiciousEntities = [
            /&lt;/gi,
            /&gt;/gi,
            /&quot;/gi,
            /&#x/gi,
            /&#\d/gi,
            /&amp;/gi
        ];
    }
    
    /**
     * Main filtering function - processes input through all security layers
     */
    filterInput(input) {
        let result = {
            original: input,
            filtered: input,
            blocked: false,
            bypassAttempted: false,
            filtersPassed: [],
            filtersBlocked: [],
            securityLevel: 'HIGH',
            warnings: [],
            bypassMethod: null,
            hiddenChallenges: [],
            fragmentsFound: [],
            logicPuzzles: [],
            steganographyClues: []
        };

        // Check for hidden patterns and logic puzzles first
        this.analyzeHiddenPatterns(input, result);

        // Apply each filter layer
        for (let filterName of this.filterLayers) {
            let filterResult = this[filterName](result.filtered);

            if (filterResult.blocked) {
                result.blocked = true;
                result.filtersBlocked.push(filterName);
                result.warnings.push(filterResult.reason);
            } else {
                result.filtersPassed.push(filterName);
                result.filtered = filterResult.output;

                // Check for hidden logic triggers
                if (filterResult.hiddenTrigger) {
                    result.hiddenChallenges.push(filterResult.hiddenTrigger);
                }
            }
        }

        // Check for bypass attempts
        result.bypassAttempted = this.detectBypassAttempt(input);

        // Enhanced bypass detection with fragment unlocking
        if (this.checkValidBypass(input)) {
            result.bypassMethod = 'HTML_ENTITY_DOUBLE_ENCODING';
            result.securityLevel = 'COMPROMISED';
            this.bypassDetected = true;

            // Check for logic puzzle solutions in the payload
            this.checkLogicPuzzleSolutions(input, result);

            // Assemble flag if enough fragments are unlocked
            if (this.fragmentsUnlocked.size >= 4) {
                result.flagRevealed = true;
                result.flag = this.assembleFinalFlag();
                setTimeout(() => this.executeAdvancedBypass(input, result), 100);
            } else {
                result.partialSuccess = true;
                result.fragmentsNeeded = 4 - this.fragmentsUnlocked.size;
                result.hint = this.generateContextualHint();
            }
        }

        return result;
    }
    
    /**
     * Basic script tag filtering
     */
    basicScriptFilter(input) {
        const scriptPattern = /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi;
        
        if (scriptPattern.test(input)) {
            return {
                blocked: true,
                reason: 'Script tags detected and blocked',
                output: input.replace(scriptPattern, '[BLOCKED_SCRIPT]')
            };
        }
        
        return { blocked: false, output: input };
    }
    
    /**
     * Event handler filtering (onclick, onload, etc.)
     */
    eventHandlerFilter(input) {
        const eventPattern = /on\w+\s*=/gi;
        
        if (eventPattern.test(input)) {
            return {
                blocked: true,
                reason: 'Event handlers detected and blocked',
                output: input.replace(eventPattern, 'blocked-event=')
            };
        }
        
        return { blocked: false, output: input };
    }
    
    /**
     * HTML entity filtering - this is where the vulnerability lies
     */
    htmlEntityFilter(input) {
        // This filter has a flaw - it doesn't handle double-encoded entities properly
        let decoded = input;
        
        // First pass - decode common entities
        decoded = decoded.replace(/&lt;/gi, '<');
        decoded = decoded.replace(/&gt;/gi, '>');
        decoded = decoded.replace(/&quot;/gi, '"');
        decoded = decoded.replace(/&amp;/gi, '&');
        
        // Check if decoded version contains dangerous patterns
        for (let pattern of this.dangerousPatterns) {
            if (pattern.test(decoded)) {
                return {
                    blocked: true,
                    reason: 'Dangerous HTML entities detected after decoding',
                    output: input.replace(/[<>"&]/g, (match) => {
                        const entities = { '<': '&lt;', '>': '&gt;', '"': '&quot;', '&': '&amp;' };
                        return entities[match] || match;
                    })
                };
            }
        }
        
        return { blocked: false, output: decoded };
    }
    
    /**
     * Unicode normalization - attempts to normalize Unicode characters
     */
    unicodeNormalization(input) {
        try {
            let normalized = input.normalize('NFKC');
            
            // Check for suspicious Unicode patterns
            if (/[\u2028\u2029\ufeff\u200b-\u200f\u202a-\u202e]/g.test(input)) {
                return {
                    blocked: true,
                    reason: 'Suspicious Unicode characters detected',
                    output: normalized.replace(/[\u2028\u2029\ufeff\u200b-\u200f\u202a-\u202e]/g, '')
                };
            }
            
            return { blocked: false, output: normalized };
        } catch (e) {
            return { blocked: false, output: input };
        }
    }
    
    /**
     * Contextual filtering based on where the input will be placed
     */
    contextualFilter(input) {
        // Simulate contextual filtering for HTML attribute context
        if (input.includes('javascript:') || input.includes('vbscript:')) {
            return {
                blocked: true,
                reason: 'Dangerous URL schemes detected',
                output: input.replace(/(javascript|vbscript):/gi, 'blocked:')
            };
        }
        
        return { blocked: false, output: input };
    }
    
    /**
     * Advanced pattern filtering for sophisticated attacks
     */
    advancedPatternFilter(input) {
        const advancedPatterns = [
            /data:text\/html/gi,
            /expression\s*\(/gi,
            /<iframe[^>]*srcdoc/gi,
            /import\s*\(/gi,
            /eval\s*\(/gi
        ];

        for (let pattern of advancedPatterns) {
            if (pattern.test(input)) {
                return {
                    blocked: true,
                    reason: 'Advanced attack pattern detected',
                    output: input.replace(pattern, '[BLOCKED_ADVANCED]')
                };
            }
        }

        return { blocked: false, output: input };
    }

    /**
     * Hidden logic filter - checks for embedded logic puzzles and steganography
     */
    hiddenLogicFilter(input) {
        let hiddenTrigger = null;

        // Check for Fibonacci sequence in payload
        const fibMatch = input.match(/(\d+),?\s*(\d+),?\s*(\d+),?\s*(\d+),?\s*(\d+)/);
        if (fibMatch) {
            const nums = fibMatch.slice(1).map(n => parseInt(n));
            if (this.isFibonacciSequence(nums)) {
                hiddenTrigger = {
                    type: 'fibonacci_sequence',
                    values: nums,
                    fragment: 'part1'
                };
                this.fragmentsUnlocked.add('part1');
            }
        }

        // Check for binary patterns
        const binaryMatch = input.match(/[01]{7,}/g);
        if (binaryMatch) {
            for (let binary of binaryMatch) {
                const decimal = parseInt(binary, 2);
                if (decimal === 120) { // Hidden challenge answer
                    hiddenTrigger = {
                        type: 'binary_conversion',
                        binary: binary,
                        decimal: decimal,
                        fragment: 'part2'
                    };
                    this.fragmentsUnlocked.add('part2');
                }
            }
        }

        // Check for Caesar cipher patterns
        const caesarMatch = input.match(/[A-Z\s]{10,}/);
        if (caesarMatch) {
            const decrypted = this.caesarDecrypt(caesarMatch[0], 3);
            if (decrypted.includes('THIS IS A TEST')) {
                hiddenTrigger = {
                    type: 'caesar_cipher',
                    encrypted: caesarMatch[0],
                    decrypted: decrypted,
                    fragment: 'part3'
                };
                this.fragmentsUnlocked.add('part3');
            }
        }

        // Check for hex patterns
        const hexMatch = input.match(/[0-9a-fA-F]{10,}/);
        if (hexMatch) {
            try {
                const ascii = this.hexToAscii(hexMatch[0]);
                if (ascii.toLowerCase().includes('hello')) {
                    hiddenTrigger = {
                        type: 'hex_conversion',
                        hex: hexMatch[0],
                        ascii: ascii,
                        fragment: 'part4'
                    };
                    this.fragmentsUnlocked.add('part4');
                }
            } catch (e) {
                // Invalid hex, ignore
            }
        }

        return {
            blocked: false,
            output: input,
            hiddenTrigger: hiddenTrigger
        };
    }
    
    /**
     * Detect if user is attempting a bypass
     */
    detectBypassAttempt(input) {
        const bypassIndicators = [
            /&amp;lt;/gi,  // Double-encoded <
            /&amp;gt;/gi,  // Double-encoded >
            /&#x/gi,       // Hex entities
            /&#\d/gi,      // Decimal entities
            /\u/gi,        // Unicode escapes
            /%3C/gi,       // URL encoded <
            /%3E/gi,       // URL encoded >
            /\\x/gi,       // Hex escapes
            /\\u/gi        // Unicode escapes
        ];
        
        return bypassIndicators.some(pattern => pattern.test(input));
    }
    
    /**
     * Check if this is a valid bypass method
     */
    checkValidBypass(input) {
        // The intended solution uses double HTML entity encoding
        // Example: &amp;lt;script&amp;gt;alert('XSS')&amp;lt;/script&amp;gt;
        const doubleEncodedPattern = /&amp;lt;.*?&amp;gt;/gi;
        return doubleEncodedPattern.test(input);
    }
    
    /**
     * Check if this is the correct bypass that should reveal the flag
     */
    isCorrectBypass(input) {
        // The correct bypass should contain a double-encoded script tag with alert
        const correctPattern = /&amp;lt;script&amp;gt;.*?alert.*?&amp;lt;\/script&amp;gt;/gi;
        return correctPattern.test(input);
    }

    /**
     * Analyze hidden patterns in the input
     */
    analyzeHiddenPatterns(input, result) {
        // Check for steganographic patterns
        if (input.includes('...') && input.includes('---')) {
            result.steganographyClues.push({
                type: 'morse_code',
                pattern: 'Morse code detected',
                hint: 'Dots and dashes hide secrets'
            });
        }

        // Check for base64 patterns
        const base64Pattern = /[A-Za-z0-9+/]{4,}={0,2}/g;
        const base64Matches = input.match(base64Pattern);
        if (base64Matches) {
            for (let match of base64Matches) {
                try {
                    const decoded = atob(match);
                    if (decoded.length > 2) {
                        result.steganographyClues.push({
                            type: 'base64_encoding',
                            encoded: match,
                            decoded: decoded,
                            hint: 'Base64 encoding detected'
                        });
                    }
                } catch (e) {
                    // Invalid base64, ignore
                }
            }
        }

        // Check for ROT13 patterns
        const rot13Pattern = /[A-Za-z]{5,}/g;
        const textMatches = input.match(rot13Pattern);
        if (textMatches) {
            for (let match of textMatches) {
                const rot13 = this.rot13(match);
                if (rot13.toLowerCase().includes('flag') || rot13.toLowerCase().includes('wolf')) {
                    result.steganographyClues.push({
                        type: 'rot13_cipher',
                        original: match,
                        decoded: rot13,
                        hint: 'ROT13 cipher detected'
                    });
                }
            }
        }
    }

    /**
     * Check for logic puzzle solutions in the payload
     */
    checkLogicPuzzleSolutions(input, result) {
        // Mathematical sequences
        const numberPattern = /\d+/g;
        const numbers = input.match(numberPattern);
        if (numbers) {
            const nums = numbers.map(n => parseInt(n));

            // Check Fibonacci
            if (this.containsFibonacci(nums)) {
                result.logicPuzzles.push({
                    type: 'fibonacci',
                    solution: 'Fibonacci sequence detected',
                    fragment: 'part1'
                });
                this.fragmentsUnlocked.add('part1');
            }

            // Check primes
            if (this.containsPrimes(nums)) {
                result.logicPuzzles.push({
                    type: 'prime_sequence',
                    solution: 'Prime number sequence detected',
                    fragment: 'bonus'
                });
            }
        }

        // Binary conversion challenges
        const binaryPattern = /[01]{6,}/g;
        const binaryMatches = input.match(binaryPattern);
        if (binaryMatches) {
            for (let binary of binaryMatches) {
                const decimal = parseInt(binary, 2);
                if (decimal >= 100 && decimal <= 130) {
                    result.logicPuzzles.push({
                        type: 'binary_conversion',
                        binary: binary,
                        decimal: decimal,
                        fragment: 'part2'
                    });
                    this.fragmentsUnlocked.add('part2');
                }
            }
        }
    }
    
    /**
     * Assemble the final flag from unlocked fragments
     */
    assembleFinalFlag() {
        if (this.fragmentsUnlocked.size < 4) {
            return null;
        }

        try {
            const part1 = atob(this.flagFragments.part1); // WOLF
            const part2 = atob(this.flagFragments.part2); // xss_byp
            const part3 = atob(this.flagFragments.part3); // 4ass_3
            const part4 = atob(this.flagFragments.part4); // xp3rt
            const sep = atob(this.flagFragments.separator); // {}

            return `${part1}${sep.charAt(0)}${part2}${part3}${part4}${sep.charAt(1)}`;
        } catch (e) {
            return 'WOLF{assembly_error}';
        }
    }

    /**
     * Generate contextual hint based on current progress
     */
    generateContextualHint() {
        const unlockedCount = this.fragmentsUnlocked.size;
        const hints = [
            "Numbers follow patterns... Fibonacci knows the way",
            "Binary speaks in 1s and 0s... 120 is the key",
            "Caesar shifted letters... 3 steps back reveals truth",
            "Hexadecimal hides ASCII... Hello opens doors"
        ];

        if (unlockedCount === 0) return "Look for mathematical sequences in your payload";
        if (unlockedCount === 1) return "Binary conversion might unlock the next piece";
        if (unlockedCount === 2) return "Ancient ciphers still work... try Caesar's method";
        if (unlockedCount === 3) return "Hexadecimal to ASCII conversion completes the puzzle";

        return "All fragments found! The flag should be revealed.";
    }

    /**
     * Execute advanced bypass with fragment analysis
     */
    executeAdvancedBypass(input, result) {
        // Decode the double-encoded payload
        let decoded = input.replace(/&amp;lt;/gi, '<').replace(/&amp;gt;/gi, '>');

        // Extract the alert content
        const alertMatch = decoded.match(/alert\s*\(\s*['"`]([^'"`]*)['"`]\s*\)/i);

        if (alertMatch) {
            this.showAdvancedSuccessMessage(alertMatch[1], result);
        }
    }

    /**
     * Show advanced success message with fragment details
     */
    showAdvancedSuccessMessage(alertContent, result) {
        const successDiv = document.createElement('div');

        let fragmentDetails = '';
        if (result.fragmentsFound && result.fragmentsFound.length > 0) {
            fragmentDetails = `
                <div style="margin: 15px 0; padding: 10px; background: rgba(0,0,0,0.2); border-radius: 5px;">
                    <h4>🧩 Flag Fragments Discovered:</h4>
                    ${result.fragmentsFound.map(f => `<p>• ${f.type}: ${f.value}</p>`).join('')}
                </div>
            `;
        }

        let puzzleDetails = '';
        if (result.logicPuzzles && result.logicPuzzles.length > 0) {
            puzzleDetails = `
                <div style="margin: 15px 0; padding: 10px; background: rgba(0,0,0,0.2); border-radius: 5px;">
                    <h4>🧠 Logic Puzzles Solved:</h4>
                    ${result.logicPuzzles.map(p => `<p>• ${p.type}: ${p.solution}</p>`).join('')}
                </div>
            `;
        }

        successDiv.innerHTML = `
            <div style="background: linear-gradient(45deg, #28a745, #20c997); color: white; padding: 20px; border-radius: 10px; margin: 20px 0; text-align: center; box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);">
                <h3>🎉 MASTER HACKER ACHIEVEMENT UNLOCKED! 🎉</h3>
                <p><strong>XSS Bypass:</strong> ✅ Successful</p>
                <p><strong>Logic Puzzles:</strong> ✅ ${this.fragmentsUnlocked.size}/4 Solved</p>
                <p><strong>Steganography:</strong> ✅ Detected</p>
                ${fragmentDetails}
                ${puzzleDetails}
                <p><strong>🏆 FINAL FLAG:</strong></p>
                <div style="background: rgba(0,0,0,0.4); padding: 15px; border-radius: 8px; font-family: 'Courier New', monospace; font-size: 24px; letter-spacing: 2px; margin: 10px 0;">
                    ${result.flag}
                </div>
                <p><em>Congratulations! You've mastered advanced XSS bypass techniques, solved complex logic puzzles, and discovered hidden steganographic patterns!</em></p>
                <div style="margin-top: 20px; font-size: 14px; opacity: 0.9;">
                    <p>🧠 Logic Mastery: Mathematical sequences, binary conversion, cryptography</p>
                    <p>🔍 Steganography Expert: Hidden patterns, encoding detection</p>
                    <p>⚡ XSS Ninja: Advanced filter bypass techniques</p>
                </div>
            </div>
        `;

        document.getElementById('filter-results').appendChild(successDiv);

        // Update security status with dramatic effect
        const statusElement = document.getElementById('security-status');
        statusElement.textContent = 'COMPLETELY COMPROMISED';
        statusElement.className = 'security-status bypassed';
        statusElement.style.animation = 'blink 0.5s infinite';
    }

    /**
     * Utility Functions for Cryptographic Operations
     */

    // Check if array contains Fibonacci sequence
    isFibonacciSequence(nums) {
        if (nums.length < 3) return false;
        for (let i = 2; i < nums.length; i++) {
            if (nums[i] !== nums[i-1] + nums[i-2]) return false;
        }
        return true;
    }

    // Check if array contains Fibonacci numbers
    containsFibonacci(nums) {
        const fibSet = new Set([1, 1, 2, 3, 5, 8, 13, 21, 34, 55, 89, 144]);
        return nums.some(n => fibSet.has(n));
    }

    // Check if array contains prime numbers
    containsPrimes(nums) {
        const primeSet = new Set([2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31, 37]);
        return nums.some(n => primeSet.has(n));
    }

    // Caesar cipher decryption
    caesarDecrypt(text, shift) {
        return text.replace(/[A-Z]/g, char => {
            return String.fromCharCode(((char.charCodeAt(0) - 65 - shift + 26) % 26) + 65);
        }).replace(/[a-z]/g, char => {
            return String.fromCharCode(((char.charCodeAt(0) - 97 - shift + 26) % 26) + 97);
        });
    }

    // ROT13 cipher
    rot13(text) {
        return text.replace(/[A-Za-z]/g, char => {
            const start = char <= 'Z' ? 65 : 97;
            return String.fromCharCode(((char.charCodeAt(0) - start + 13) % 26) + start);
        });
    }

    // Hex to ASCII conversion
    hexToAscii(hex) {
        let result = '';
        for (let i = 0; i < hex.length; i += 2) {
            result += String.fromCharCode(parseInt(hex.substr(i, 2), 16));
        }
        return result;
    }

    // Binary to ASCII conversion
    binaryToAscii(binary) {
        return binary.split(' ').map(bin =>
            String.fromCharCode(parseInt(bin, 2))
        ).join('');
    }

    // Check if number is prime
    isPrime(n) {
        if (n < 2) return false;
        for (let i = 2; i <= Math.sqrt(n); i++) {
            if (n % i === 0) return false;
        }
        return true;
    }

    // Generate Fibonacci sequence
    generateFibonacci(n) {
        const fib = [1, 1];
        for (let i = 2; i < n; i++) {
            fib[i] = fib[i-1] + fib[i-2];
        }
        return fib;
    }

    // Morse code decoder
    morseToText(morse) {
        const morseCode = {
            '.-': 'A', '-...': 'B', '-.-.': 'C', '-..': 'D', '.': 'E',
            '..-.': 'F', '--.': 'G', '....': 'H', '..': 'I', '.---': 'J',
            '-.-': 'K', '.-..': 'L', '--': 'M', '-.': 'N', '---': 'O',
            '.--.': 'P', '--.-': 'Q', '.-.': 'R', '...': 'S', '-': 'T',
            '..-': 'U', '...-': 'V', '.--': 'W', '-..-': 'X', '-.--': 'Y',
            '--..': 'Z', '-----': '0', '.----': '1', '..---': '2',
            '...--': '3', '....-': '4', '.....': '5', '-....': '6',
            '--...': '7', '---..': '8', '----.': '9'
        };

        return morse.split(' ').map(code => morseCode[code] || '').join('');
    }

    // Advanced pattern detection for steganography
    detectSteganography(input) {
        const patterns = [];

        // Check for hidden Unicode characters
        if (/[\u200b-\u200f\u202a-\u202e\ufeff]/g.test(input)) {
            patterns.push({
                type: 'unicode_steganography',
                description: 'Hidden Unicode characters detected'
            });
        }

        // Check for whitespace patterns
        const whitespacePattern = /\s{3,}/g;
        if (whitespacePattern.test(input)) {
            patterns.push({
                type: 'whitespace_steganography',
                description: 'Suspicious whitespace patterns'
            });
        }

        // Check for repeated character patterns
        const repeatPattern = /(.)\1{4,}/g;
        if (repeatPattern.test(input)) {
            patterns.push({
                type: 'character_repetition',
                description: 'Repeated character patterns detected'
            });
        }

        return patterns;
    }
}

// Initialize the enhanced filter system with protection
(function() {
    'use strict';

    // Create protected instance
    const filterInstance = new AdvancedXSSFilter();

    // Protect the filter instance from direct access
    Object.defineProperty(window, 'xssFilter', {
        get: function() {
            // Check if anti-cheat system is active
            if (typeof window.antiCheat !== 'undefined' && window.antiCheat.violations > 3) {
                console.log('%cAccess denied due to security violations', 'color: red; font-size: 14px;');
                return null;
            }
            return filterInstance;
        },
        set: function() {
            if (window.antiCheat) {
                window.antiCheat.recordViolation('Attempted to modify XSS filter');
            }
            console.log('%cFilter modification blocked!', 'color: red; font-size: 14px;');
        },
        configurable: false
    });

    // Additional protection: Hide the class constructor
    window.AdvancedXSSFilter = undefined;

    // Protect against common tampering
    const originalStringify = JSON.stringify;
    JSON.stringify = function(obj) {
        if (obj && obj.constructor && obj.constructor.name === 'AdvancedXSSFilter') {
            if (window.antiCheat) {
                window.antiCheat.recordViolation('Attempted to serialize filter object');
            }
            return '{"error": "Access denied"}';
        }
        return originalStringify.apply(this, arguments);
    };
})();

// Add hidden Easter eggs in comments for advanced users
/*
Hidden clues for the truly dedicated:
- Fibonacci: 1,1,2,3,5,8,13,21,34
- Binary: 1111000 = 120
- Caesar: WKLV LV D WHVW (shift 3) = THIS IS A TEST
- Hex: 48656c6c6f = Hello
- Base64 fragments are scattered in the code
- The final assembly requires all 4 parts
- V09MRg== + eHNzX2J5cA== + NGFzc18z + eHAzcnQ= = ?
*/
