/**
 * Advanced XSS Security Filter System
 * WOLF CTF - Problem 3: XSS Payload Challenge
 * 
 * This filter implements multiple layers of security to prevent XSS attacks.
 * However, it has specific vulnerabilities that can be exploited using advanced techniques.
 */

class AdvancedXSSFilter {
    constructor() {
        this.filterLayers = [
            'basicScriptFilter',
            'eventHandlerFilter', 
            'htmlEntityFilter',
            'unicodeNormalization',
            'contextualFilter',
            'advancedPatternFilter'
        ];
        
        this.bypassDetected = false;
        this.flagRevealed = false;
        this.correctFlag = 'WOLF{xss_byp4ss_3xp3rt}';
        
        // Dangerous patterns to block
        this.dangerousPatterns = [
            /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
            /javascript:/gi,
            /on\w+\s*=/gi,
            /<iframe\b[^>]*>/gi,
            /<object\b[^>]*>/gi,
            /<embed\b[^>]*>/gi,
            /<link\b[^>]*>/gi,
            /<meta\b[^>]*>/gi,
            /expression\s*\(/gi,
            /vbscript:/gi,
            /data:text\/html/gi
        ];
        
        // HTML entities that might be used for bypass
        this.suspiciousEntities = [
            /&lt;/gi,
            /&gt;/gi,
            /&quot;/gi,
            /&#x/gi,
            /&#\d/gi,
            /&amp;/gi
        ];
    }
    
    /**
     * Main filtering function - processes input through all security layers
     */
    filterInput(input) {
        let result = {
            original: input,
            filtered: input,
            blocked: false,
            bypassAttempted: false,
            filtersPassed: [],
            filtersBlocked: [],
            securityLevel: 'HIGH',
            warnings: [],
            bypassMethod: null
        };
        
        // Apply each filter layer
        for (let filterName of this.filterLayers) {
            let filterResult = this[filterName](result.filtered);
            
            if (filterResult.blocked) {
                result.blocked = true;
                result.filtersBlocked.push(filterName);
                result.warnings.push(filterResult.reason);
            } else {
                result.filtersPassed.push(filterName);
                result.filtered = filterResult.output;
            }
        }
        
        // Check for bypass attempts
        result.bypassAttempted = this.detectBypassAttempt(input);
        
        // Special bypass detection for the intended solution
        if (this.checkValidBypass(input)) {
            result.bypassMethod = 'HTML_ENTITY_DOUBLE_ENCODING';
            result.securityLevel = 'COMPROMISED';
            this.bypassDetected = true;
            
            // Execute the payload if it's the correct bypass
            if (this.isCorrectBypass(input)) {
                result.flagRevealed = true;
                result.flag = this.correctFlag;
                // Trigger XSS execution
                setTimeout(() => this.executeBypass(input), 100);
            }
        }
        
        return result;
    }
    
    /**
     * Basic script tag filtering
     */
    basicScriptFilter(input) {
        const scriptPattern = /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi;
        
        if (scriptPattern.test(input)) {
            return {
                blocked: true,
                reason: 'Script tags detected and blocked',
                output: input.replace(scriptPattern, '[BLOCKED_SCRIPT]')
            };
        }
        
        return { blocked: false, output: input };
    }
    
    /**
     * Event handler filtering (onclick, onload, etc.)
     */
    eventHandlerFilter(input) {
        const eventPattern = /on\w+\s*=/gi;
        
        if (eventPattern.test(input)) {
            return {
                blocked: true,
                reason: 'Event handlers detected and blocked',
                output: input.replace(eventPattern, 'blocked-event=')
            };
        }
        
        return { blocked: false, output: input };
    }
    
    /**
     * HTML entity filtering - this is where the vulnerability lies
     */
    htmlEntityFilter(input) {
        // This filter has a flaw - it doesn't handle double-encoded entities properly
        let decoded = input;
        
        // First pass - decode common entities
        decoded = decoded.replace(/&lt;/gi, '<');
        decoded = decoded.replace(/&gt;/gi, '>');
        decoded = decoded.replace(/&quot;/gi, '"');
        decoded = decoded.replace(/&amp;/gi, '&');
        
        // Check if decoded version contains dangerous patterns
        for (let pattern of this.dangerousPatterns) {
            if (pattern.test(decoded)) {
                return {
                    blocked: true,
                    reason: 'Dangerous HTML entities detected after decoding',
                    output: input.replace(/[<>"&]/g, (match) => {
                        const entities = { '<': '&lt;', '>': '&gt;', '"': '&quot;', '&': '&amp;' };
                        return entities[match] || match;
                    })
                };
            }
        }
        
        return { blocked: false, output: decoded };
    }
    
    /**
     * Unicode normalization - attempts to normalize Unicode characters
     */
    unicodeNormalization(input) {
        try {
            let normalized = input.normalize('NFKC');
            
            // Check for suspicious Unicode patterns
            if (/[\u2028\u2029\ufeff\u200b-\u200f\u202a-\u202e]/g.test(input)) {
                return {
                    blocked: true,
                    reason: 'Suspicious Unicode characters detected',
                    output: normalized.replace(/[\u2028\u2029\ufeff\u200b-\u200f\u202a-\u202e]/g, '')
                };
            }
            
            return { blocked: false, output: normalized };
        } catch (e) {
            return { blocked: false, output: input };
        }
    }
    
    /**
     * Contextual filtering based on where the input will be placed
     */
    contextualFilter(input) {
        // Simulate contextual filtering for HTML attribute context
        if (input.includes('javascript:') || input.includes('vbscript:')) {
            return {
                blocked: true,
                reason: 'Dangerous URL schemes detected',
                output: input.replace(/(javascript|vbscript):/gi, 'blocked:')
            };
        }
        
        return { blocked: false, output: input };
    }
    
    /**
     * Advanced pattern filtering for sophisticated attacks
     */
    advancedPatternFilter(input) {
        const advancedPatterns = [
            /data:text\/html/gi,
            /expression\s*\(/gi,
            /<iframe[^>]*srcdoc/gi,
            /import\s*\(/gi,
            /eval\s*\(/gi
        ];
        
        for (let pattern of advancedPatterns) {
            if (pattern.test(input)) {
                return {
                    blocked: true,
                    reason: 'Advanced attack pattern detected',
                    output: input.replace(pattern, '[BLOCKED_ADVANCED]')
                };
            }
        }
        
        return { blocked: false, output: input };
    }
    
    /**
     * Detect if user is attempting a bypass
     */
    detectBypassAttempt(input) {
        const bypassIndicators = [
            /&amp;lt;/gi,  // Double-encoded <
            /&amp;gt;/gi,  // Double-encoded >
            /&#x/gi,       // Hex entities
            /&#\d/gi,      // Decimal entities
            /\u/gi,        // Unicode escapes
            /%3C/gi,       // URL encoded <
            /%3E/gi,       // URL encoded >
            /\\x/gi,       // Hex escapes
            /\\u/gi        // Unicode escapes
        ];
        
        return bypassIndicators.some(pattern => pattern.test(input));
    }
    
    /**
     * Check if this is a valid bypass method
     */
    checkValidBypass(input) {
        // The intended solution uses double HTML entity encoding
        // Example: &amp;lt;script&amp;gt;alert('XSS')&amp;lt;/script&amp;gt;
        const doubleEncodedPattern = /&amp;lt;.*?&amp;gt;/gi;
        return doubleEncodedPattern.test(input);
    }
    
    /**
     * Check if this is the correct bypass that should reveal the flag
     */
    isCorrectBypass(input) {
        // The correct bypass should contain a double-encoded script tag with alert
        const correctPattern = /&amp;lt;script&amp;gt;.*?alert.*?&amp;lt;\/script&amp;gt;/gi;
        return correctPattern.test(input);
    }
    
    /**
     * Execute the bypass payload (simulate XSS execution)
     */
    executeBypass(input) {
        // Decode the double-encoded payload
        let decoded = input.replace(/&amp;lt;/gi, '<').replace(/&amp;gt;/gi, '>');
        
        // Extract the alert content
        const alertMatch = decoded.match(/alert\s*\(\s*['"`]([^'"`]*)['"`]\s*\)/i);
        
        if (alertMatch) {
            // Show success message instead of actual alert for security
            this.showSuccessMessage(alertMatch[1]);
        }
    }
    
    /**
     * Show success message when bypass is successful
     */
    showSuccessMessage(alertContent) {
        const successDiv = document.createElement('div');
        successDiv.innerHTML = `
            <div style="background: linear-gradient(45deg, #28a745, #20c997); color: white; padding: 20px; border-radius: 10px; margin: 20px 0; text-align: center; box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);">
                <h3>🎉 XSS BYPASS SUCCESSFUL! 🎉</h3>
                <p><strong>Alert Content:</strong> "${alertContent}"</p>
                <p><strong>Flag:</strong> <span style="background: rgba(0,0,0,0.3); padding: 5px 10px; border-radius: 5px; font-family: monospace;">${this.correctFlag}</span></p>
                <p><em>Congratulations! You successfully bypassed the advanced XSS filter using double HTML entity encoding.</em></p>
            </div>
        `;
        
        document.getElementById('filter-results').appendChild(successDiv);
        
        // Update security status
        const statusElement = document.getElementById('security-status');
        statusElement.textContent = 'BYPASSED';
        statusElement.className = 'security-status bypassed';
    }
}

// Initialize the filter system
window.xssFilter = new AdvancedXSSFilter();
