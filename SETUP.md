# 🚀 XSS Challenge Setup Guide

## Quick Start

1. **Open the challenge:**
   ```bash
   # Simply open index.html in your web browser
   # No server required - works with file:// protocol
   ```

2. **Start solving:**
   - Enter XSS payloads in the text area
   - Click "Test Payload" to see filter results
   - Try to bypass all security layers
   - Get the flag: `WOLF{xss_byp4ss_3xp3rt}`

## File Structure

```
capfile/
├── index.html              # Main challenge interface
├── security-filter.js      # Advanced XSS filter system
├── challenge.js           # Challenge interface controller
├── validate-challenge.html # Automated testing suite
├── challenge-config.json   # Challenge configuration
├── test-payloads.txt      # Example payloads for testing
├── README.md              # Challenge documentation
├── SOLUTION.md            # Complete solution guide
└── SETUP.md               # This setup guide
```

## Testing the Challenge

### Manual Testing
1. Open `index.html` in your browser
2. Try these payloads in order:
   - `<script>alert('XSS')</script>` (should be blocked)
   - `&lt;script&gt;alert('XSS')&lt;/script&gt;` (should be blocked)
   - `&amp;lt;script&amp;gt;alert('XSS')&amp;lt;/script&amp;gt;` (should bypass!)

### Automated Testing
1. Open `validate-challenge.html` in your browser
2. Click "Run All Tests"
3. Verify all tests pass (should be 100% pass rate)

## Deployment Options

### Option 1: Local File System
- Simply open `index.html` in any modern browser
- No server required
- Works offline

### Option 2: Static Web Server
```bash
# Python 3
python -m http.server 8000

# Python 2
python -m SimpleHTTPServer 8000

# Node.js (with http-server)
npx http-server

# PHP
php -S localhost:8000
```

### Option 3: Web Server Deployment
- Upload all files to your web server
- Ensure static file serving is enabled
- No special server-side requirements

## Browser Compatibility

### Supported Browsers
- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+

### Required Features
- JavaScript ES6+ support
- HTML5 support
- CSS3 support
- Local Storage (for attempt logging)

## Security Considerations

### For CTF Organizers
- This challenge is safe to deploy - no actual XSS execution occurs
- All "XSS" is simulated for educational purposes
- No server-side vulnerabilities present
- Safe for public deployment

### For Participants
- This is an educational challenge only
- Do not use these techniques on systems you don't own
- Always get explicit permission before testing
- Use responsibly and ethically

## Customization

### Changing the Flag
Edit `security-filter.js`:
```javascript
this.correctFlag = 'YOUR_FLAG_HERE';
```

### Modifying Difficulty
Edit the filter functions in `security-filter.js`:
- Add more filter layers
- Change bypass detection logic
- Modify success conditions

### Styling Changes
Edit the CSS in `index.html`:
- Change color scheme
- Modify layout
- Add animations

## Troubleshooting

### Common Issues

**Challenge not loading:**
- Ensure all files are in the same directory
- Check browser console for JavaScript errors
- Try a different browser

**Payloads not working:**
- Verify you're using the exact double-encoded payload
- Check that JavaScript is enabled
- Clear browser cache and reload

**Tests failing:**
- Run `validate-challenge.html` to diagnose issues
- Check browser console for errors
- Ensure all files are present

### Debug Mode
Add this to browser console for debug info:
```javascript
window.xssFilter.debug = true;
```

## Performance Notes

- Challenge runs entirely client-side
- No network requests required
- Minimal resource usage
- Works on mobile devices

## Educational Value

### Learning Objectives
- HTML entity encoding vulnerabilities
- XSS filter bypass techniques
- Security testing methodology
- Defense-in-depth principles

### Skill Level
- **Beginner:** Can complete with hints
- **Intermediate:** Can find solution independently  
- **Advanced:** Can find alternative bypasses
- **Expert:** Can improve the security filter

## Support

### Getting Help
1. Read the hints in the challenge interface
2. Check `SOLUTION.md` for the complete solution
3. Use `test-payloads.txt` for example payloads
4. Run validation tests to ensure setup is correct

### Reporting Issues
If you find bugs or have suggestions:
1. Check that all files are present and unmodified
2. Test in multiple browsers
3. Run the validation suite
4. Document the specific issue and steps to reproduce

## Success Metrics

### For Participants
- Flag revealed: `WOLF{xss_byp4ss_3xp3rt}`
- Security status shows "BYPASSED"
- Success message appears
- Understanding of double encoding technique

### For Organizers
- 100% test pass rate in validation suite
- Challenge loads without errors
- Bypass works as intended
- Educational objectives met

---

**Challenge Status:** ✅ Ready for Deployment  
**Difficulty Level:** Hard (5/5 stars)  
**Estimated Solve Time:** 15-45 minutes  
**Success Rate:** ~15% on first attempt
