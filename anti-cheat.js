/**
 * Advanced Anti-Cheat Protection System
 * WOLF CTF - XSS Challenge Security Layer
 * 
 * This script implements comprehensive protection against:
 * - Right-click context menu
 * - Developer tools access
 * - View source attempts
 * - Element inspection
 * - Console access
 * - Text selection and copying
 * - Printing and saving
 */

class AntiCheatSystem {
    constructor() {
        this.violations = 0;
        this.maxViolations = 5;
        this.isDevToolsOpen = false;
        this.protectionActive = true;
        
        this.init();
    }
    
    init() {
        this.blockRightClick();
        this.blockKeyboardShortcuts();
        this.blockTextSelection();
        this.detectDevTools();
        this.blockPrinting();
        this.obfuscateConsole();
        this.protectSourceCode();
        this.addVisualIndicators();
        
        console.log('%c🛡️ WOLF CTF Anti-Cheat System Initialized', 'color: red; font-size: 16px; font-weight: bold;');
    }
    
    blockRightClick() {
        document.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            this.recordViolation('Right-click context menu blocked');
            this.showWarning('🚫 Right-click disabled! Solve the challenge legitimately.');
            return false;
        });
    }
    
    blockKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            const blockedKeys = [
                { key: 123, name: 'F12 (Developer Tools)' },
                { ctrl: true, shift: true, key: 73, name: 'Ctrl+Shift+I (Inspector)' },
                { ctrl: true, shift: true, key: 74, name: 'Ctrl+Shift+J (Console)' },
                { ctrl: true, shift: true, key: 67, name: 'Ctrl+Shift+C (Element Selector)' },
                { ctrl: true, shift: true, key: 75, name: 'Ctrl+Shift+K (Firefox Console)' },
                { ctrl: true, key: 85, name: 'Ctrl+U (View Source)' },
                { ctrl: true, key: 83, name: 'Ctrl+S (Save Page)' },
                { ctrl: true, key: 80, name: 'Ctrl+P (Print)' },
                { ctrl: true, key: 65, name: 'Ctrl+A (Select All)' },
                { key: 116, name: 'F5 (Refresh)' } // Optional: block refresh
            ];
            
            for (let blocked of blockedKeys) {
                if (this.matchesKeyCombo(e, blocked)) {
                    e.preventDefault();
                    e.stopPropagation();
                    this.recordViolation(`Blocked: ${blocked.name}`);
                    this.showWarning(`🚫 ${blocked.name} is disabled!`);
                    return false;
                }
            }
        });
    }
    
    matchesKeyCombo(event, combo) {
        return (
            event.keyCode === combo.key &&
            (!combo.ctrl || event.ctrlKey) &&
            (!combo.shift || event.shiftKey) &&
            (!combo.alt || event.altKey)
        );
    }
    
    blockTextSelection() {
        // Prevent text selection except in input fields
        document.addEventListener('selectstart', (e) => {
            if (!['INPUT', 'TEXTAREA'].includes(e.target.tagName)) {
                e.preventDefault();
                return false;
            }
        });
        
        // Prevent drag and drop
        document.addEventListener('dragstart', (e) => {
            if (!['INPUT', 'TEXTAREA'].includes(e.target.tagName)) {
                e.preventDefault();
                return false;
            }
        });
    }
    
    detectDevTools() {
        // Method 1: Window size detection
        const threshold = 160;
        setInterval(() => {
            const widthThreshold = window.outerWidth - window.innerWidth > threshold;
            const heightThreshold = window.outerHeight - window.innerHeight > threshold;
            
            if (widthThreshold || heightThreshold) {
                if (!this.isDevToolsOpen) {
                    this.isDevToolsOpen = true;
                    this.recordViolation('Developer Tools detected (window size)');
                    this.showWarning('🚨 Developer Tools detected! Challenge integrity compromised.');
                    this.handleDevToolsDetection();
                }
            } else {
                this.isDevToolsOpen = false;
            }
        }, 1000);
        
        // Method 2: Console detection
        let devtools = { toString: () => {
            this.recordViolation('Console access detected');
            this.showWarning('🚨 Console access detected!');
            this.handleDevToolsDetection();
        }};
        
        setInterval(() => {
            console.log('%c', devtools);
        }, 2000);
        
        // Method 3: Debugger detection
        setInterval(() => {
            const start = performance.now();
            debugger;
            const end = performance.now();
            
            if (end - start > 100) {
                this.recordViolation('Debugger detected');
                this.showWarning('🚨 Debugger detected!');
                this.handleDevToolsDetection();
            }
        }, 3000);
    }
    
    blockPrinting() {
        // Block print dialog
        window.addEventListener('beforeprint', (e) => {
            e.preventDefault();
            this.recordViolation('Print attempt blocked');
            this.showWarning('🚫 Printing disabled for security!');
            return false;
        });
        
        // Block Ctrl+P specifically
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.keyCode === 80) {
                e.preventDefault();
                this.recordViolation('Print shortcut blocked');
                this.showWarning('🚫 Printing disabled!');
                return false;
            }
        });
    }
    
    obfuscateConsole() {
        // Clear console periodically
        setInterval(() => {
            console.clear();
            console.log('%c🐺 WOLF CTF - ANTI-CHEAT SYSTEM', 'color: red; font-size: 20px; font-weight: bold; text-shadow: 2px 2px 4px rgba(0,0,0,0.5);');
            console.log('%c⚠️ UNAUTHORIZED ACCESS DETECTED ⚠️', 'color: orange; font-size: 16px; font-weight: bold;');
            console.log('%cSolve the challenge legitimately by crafting XSS payloads!', 'color: yellow; font-size: 14px;');
            console.log('%cViolations: ' + this.violations, 'color: red; font-size: 12px;');
        }, 2000);
        
        // Override console methods
        const originalMethods = {
            log: console.log,
            info: console.info,
            warn: console.warn,
            error: console.error,
            debug: console.debug
        };
        
        // Disable most console output
        console.log = () => {};
        console.info = () => {};
        console.warn = () => {};
        console.error = () => {};
        console.debug = () => {};
        
        // Allow only our security messages
        window.secureLog = (message, style) => {
            originalMethods.log(message, style);
        };
    }
    
    protectSourceCode() {
        // Hide script content after page load
        setTimeout(() => {
            const scripts = document.querySelectorAll('script:not([src])');
            scripts.forEach(script => {
                if (!script.src && script.textContent.includes('AntiCheatSystem')) {
                    // Keep anti-cheat script but obfuscate others
                    return;
                }
                script.textContent = '/* Script content protected */';
            });
        }, 2000);
        
        // Protect global objects
        try {
            Object.defineProperty(window, 'xssFilter', {
                get: () => {
                    this.recordViolation('Direct filter access attempt');
                    this.showWarning('🚫 Direct access to challenge objects blocked!');
                    return undefined;
                },
                set: () => {
                    this.recordViolation('Filter modification attempt');
                    this.showWarning('🚫 Modification of challenge objects blocked!');
                }
            });
        } catch (e) {
            // Object might already be defined
        }
    }
    
    addVisualIndicators() {
        // Update security indicator
        const indicator = document.getElementById('security-indicator');
        if (indicator) {
            setInterval(() => {
                const status = this.protectionActive ? 'ACTIVE' : 'COMPROMISED';
                const color = this.protectionActive ? 'rgba(255, 0, 0, 0.8)' : 'rgba(255, 165, 0, 0.8)';
                indicator.textContent = `🛡️ ANTI-CHEAT ${status}`;
                indicator.style.background = color;
            }, 1000);
        }
    }
    
    recordViolation(type) {
        this.violations++;
        const timestamp = new Date().toISOString();
        
        // Log violation (in a real scenario, this would be sent to a server)
        const violation = {
            timestamp,
            type,
            userAgent: navigator.userAgent,
            url: window.location.href,
            violations: this.violations
        };
        
        // Store in localStorage for demo
        let violations = JSON.parse(localStorage.getItem('ctf-violations') || '[]');
        violations.push(violation);
        localStorage.setItem('ctf-violations', JSON.stringify(violations));
        
        // Check if max violations reached
        if (this.violations >= this.maxViolations) {
            this.handleMaxViolations();
        }
    }
    
    showWarning(message) {
        const warningDiv = document.createElement('div');
        warningDiv.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: linear-gradient(45deg, #ff0000, #cc0000);
            color: white;
            padding: 20px 30px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(255, 0, 0, 0.6);
            z-index: 10000;
            font-family: 'Courier New', monospace;
            font-weight: bold;
            text-align: center;
            border: 3px solid #ffffff;
            animation: shake 0.6s ease-in-out, fadeIn 0.3s ease-in;
            max-width: 400px;
        `;
        
        warningDiv.innerHTML = `
            <div style="font-size: 24px; margin-bottom: 10px;">🚨</div>
            <h3 style="margin: 0 0 10px 0; color: #ffff00;">SECURITY VIOLATION</h3>
            <p style="margin: 0 0 15px 0;">${message}</p>
            <div style="font-size: 12px; opacity: 0.9; border-top: 1px solid rgba(255,255,255,0.3); padding-top: 10px;">
                Violations: ${this.violations}/${this.maxViolations}<br>
                Auto-dismiss in 4 seconds
            </div>
        `;
        
        document.body.appendChild(warningDiv);
        
        // Auto-remove after 4 seconds
        setTimeout(() => {
            if (warningDiv.parentNode) {
                warningDiv.style.animation = 'fadeOut 0.3s ease-out';
                setTimeout(() => {
                    if (warningDiv.parentNode) {
                        warningDiv.parentNode.removeChild(warningDiv);
                    }
                }, 300);
            }
        }, 4000);
    }
    
    handleDevToolsDetection() {
        this.protectionActive = false;
        
        // Optional: More aggressive response
        // setTimeout(() => {
        //     window.location.href = 'about:blank';
        // }, 5000);
    }
    
    handleMaxViolations() {
        this.showWarning(`🚨 MAXIMUM VIOLATIONS REACHED! (${this.maxViolations})`);
        
        // Optional: Disable challenge or redirect
        // setTimeout(() => {
        //     document.body.innerHTML = '<h1 style="color: red; text-align: center; margin-top: 200px;">CHALLENGE DISABLED DUE TO SECURITY VIOLATIONS</h1>';
        // }, 3000);
    }
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes shake {
        0%, 100% { transform: translate(-50%, -50%) translateX(0); }
        25% { transform: translate(-50%, -50%) translateX(-8px); }
        75% { transform: translate(-50%, -50%) translateX(8px); }
    }
    
    @keyframes fadeIn {
        from { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
        to { opacity: 1; transform: translate(-50%, -50%) scale(1); }
    }
    
    @keyframes fadeOut {
        from { opacity: 1; transform: translate(-50%, -50%) scale(1); }
        to { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
    }
`;
document.head.appendChild(style);

// Initialize anti-cheat system when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.antiCheat = new AntiCheatSystem();
});

// Additional protection: Prevent common bypass attempts
(function() {
    'use strict';
    
    // Prevent iframe embedding
    if (window.top !== window.self) {
        window.top.location = window.self.location;
    }
    
    // Detect automation tools
    if (navigator.webdriver) {
        console.log('%cAutomation tool detected!', 'color: red; font-size: 16px;');
    }
    
    // Basic obfuscation
    const _0x1234 = ['contextmenu', 'keydown', 'selectstart', 'dragstart'];
    _0x1234.forEach(event => {
        document.addEventListener(event, function(e) {
            if (event === 'contextmenu') e.preventDefault();
        });
    });
})();
