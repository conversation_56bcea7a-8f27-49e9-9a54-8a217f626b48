<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XSS Payload Challenge - WOLF CTF</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: linear-gradient(135deg, #000000, #000000);
            color: #fff;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #ff6b6b;
            padding-bottom: 20px;
        }
        
        .challenge-info {
            background: rgba(255, 107, 107, 0.1);
            border-left: 4px solid #ff6b6b;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        
        .input-section {
            background: rgba(255, 255, 255, 0.05);
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .payload-input {
            width: 100%;
            height: 100px;
            background: #1a1a1a;
            color: #00ff00;
            border: 2px solid #333;
            border-radius: 5px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            resize: vertical;
        }
        
        .payload-input:focus {
            outline: none;
            border-color: #ff6b6b;
            box-shadow: 0 0 10px rgba(255, 107, 107, 0.3);
        }
        
        .submit-btn {
            background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            margin-top: 10px;
        }
        
        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
        }
        
        .output-section {
            background: rgba(255, 255, 255, 0.05);
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
            min-height: 100px;
        }
        
        .filter-info {
            background: rgba(255, 193, 7, 0.1);
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        
        .hint-section {
            background: rgba(40, 167, 69, 0.1);
            border-left: 4px solid #28a745;
            padding: 15px;
            margin-top: 20px;
            border-radius: 5px;
        }
        
        .security-status {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: bold;
            margin-left: 10px;
        }
        
        .secure {
            background: #28a745;
            color: white;
        }
        
        .bypassed {
            background: #dc3545;
            color: white;
            animation: blink 1s infinite;
        }
        
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.5; }
        }
        
        .code {
            background: #1a1a1a;
            color: #00ff00;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        
        .warning {
            color: #ffc107;
            font-weight: bold;
        }
        
        .success {
            color: #28a745;
            font-weight: bold;
        }
        
        .error {
            color: #dc3545;
            font-weight: bold;
        }

        /* Enhanced Anti-Cheat Protection Styles */
        * {
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            /* Additional right-click protection */
            -webkit-touch-callout: none;
            -webkit-tap-highlight-color: transparent;
        }

        /* Additional protection styles */
        html, body, * {
            -webkit-touch-callout: none;
            -webkit-tap-highlight-color: rgba(0,0,0,0);
        }

        /* Prevent right-click highlighting */
        *::selection {
            background: transparent;
        }

        *::-moz-selection {
            background: transparent;
        }

        input, textarea {
            -webkit-user-select: text !important;
            -moz-user-select: text !important;
            -ms-user-select: text !important;
            user-select: text !important;
        }

        .security-indicator {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(255, 0, 0, 0.8);
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            z-index: 9999;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.8; }
            50% { opacity: 1; }
        }

        .no-select {
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -khtml-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        .protected-content {
            pointer-events: none;
        }

        .protected-content input,
        .protected-content textarea,
        .protected-content button {
            pointer-events: auto;
        }
    </style>

    <!-- Immediate Right-Click Protection (runs before page loads) -->
    <script>
        (function() {
            'use strict';

            // Immediate right-click blocking - runs as soon as script loads
            const blockRightClick = function(e) {
                if (e.button === 2 || e.which === 3 || e.type === 'contextmenu') {
                    e.preventDefault();
                    e.stopPropagation();
                    if (e.stopImmediatePropagate) e.stopImmediatePropagate();

                    // Show immediate warning
                    alert('🚫 Right-click is disabled for security purposes!');
                    return false;
                }
            };

            // Apply blocking immediately
            document.addEventListener('contextmenu', blockRightClick, true);
            document.addEventListener('mousedown', blockRightClick, true);
            document.addEventListener('mouseup', blockRightClick, true);

            // Also block on window
            window.addEventListener('contextmenu', blockRightClick, true);

            // Override properties immediately
            document.oncontextmenu = blockRightClick;
            window.oncontextmenu = blockRightClick;

            // Additional protection methods
            if (typeof document.onselectstart !== "undefined") {
                document.onselectstart = function() { return false; };
            }

            if (typeof document.ondragstart !== "undefined") {
                document.ondragstart = function() { return false; };
            }

            // Prevent common right-click bypass attempts
            document.addEventListener('keydown', function(e) {
                // Block Shift+F10 (context menu key)
                if (e.shiftKey && e.keyCode === 121) {
                    e.preventDefault();
                    alert('🚫 Context menu key blocked!');
                    return false;
                }
                // Block Menu key (context menu)
                if (e.keyCode === 93) {
                    e.preventDefault();
                    alert('🚫 Menu key blocked!');
                    return false;
                }
            }, true);

            // Block as soon as DOM starts loading
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', function() {
                    document.body.oncontextmenu = blockRightClick;
                    document.body.addEventListener('contextmenu', blockRightClick, true);
                    document.body.addEventListener('mousedown', blockRightClick, true);
                });
            } else {
                // DOM already loaded
                if (document.body) {
                    document.body.oncontextmenu = blockRightClick;
                    document.body.addEventListener('contextmenu', blockRightClick, true);
                    document.body.addEventListener('mousedown', blockRightClick, true);
                }
            }
        })();
    </script>
</head>
<body class="no-select" oncontextmenu="return false;" ondragstart="return false;" onselectstart="return false;">
    <!-- Security Indicator -->
    <div class="security-indicator" id="security-indicator">
        🛡️ ANTI-CHEAT ACTIVE
    </div>

    <div class="container protected-content">
        <div class="header">
            <h1>🐺 WOLF CTF - XSS Payload Challenge</h1>
            <h2>Problem 3: Master Hacker's Ultimate Test</h2>
            <div class="challenge-info">
                <strong>Difficulty:</strong> Easy | <strong>Points:</strong> 75 | <strong>Flag Format:</strong> WOLF{...}
                <br><em>⚠️ Warning: This challenge requires advanced XSS bypass skills, cryptographic knowledge, and logical reasoning!</em>
            </div>
        </div>
        
        <div class="filter-info">
            <h3>🛡️ Advanced Security System Status</h3>
            <p>Multi-Layer XSS Protection: <span class="security-status secure" id="security-status">MAXIMUM SECURITY</span></p>
            <p><strong>Active Defenses:</strong> 7-layer filtering system with HTML entity processing, script blocking, event handler detection, Unicode normalization, contextual filtering, advanced pattern matching, and hidden logic validation.</p>
            <p><strong>🧩 Hidden Challenge:</strong> The flag is fragmented and scattered across multiple logical layers. Only master hackers who can solve cryptographic puzzles while bypassing security filters will succeed!</p>
        </div>
        
        <div class="input-section">
            <h3>💉 Payload Testing Ground</h3>
            <p>Enter your XSS payload below. The system will process it through our advanced security filters.</p>
            <textarea class="payload-input" id="payload-input" placeholder="Enter your XSS payload here...
Example: <script>alert('XSS')</script>"></textarea>
            <br>
            <button class="submit-btn" onclick="testPayload()">🚀 Test Payload</button>
            <button class="submit-btn" onclick="clearOutput()" style="background: linear-gradient(45deg, #6c757d, #868e96);">🗑️ Clear</button>
        </div>
        
        <div class="output-section">
            <h3>📊 Filter Analysis Results</h3>
            <div id="filter-results">
                <p><em>No payload tested yet. Enter a payload above and click "Test Payload" to see the filtering results.</em></p>
            </div>
        </div>
        
        <div class="hint-section">
            <h3>💡 Master Hacker's Hints</h3>
            <ul>
                <li><strong>🔐 Cryptographic Mastery:</strong> The flag is split into base64-encoded fragments</li>
                <li><strong>🧮 Mathematical Sequences:</strong> Fibonacci numbers unlock secrets (1,1,2,3,5,8,13,21,34...)</li>
                <li><strong>💻 Binary Conversion:</strong> Decimal 120 in binary holds a key</li>
                <li><strong>🏛️ Ancient Ciphers:</strong> Caesar shifted by 3 reveals hidden messages</li>
                <li><strong>🔢 Hexadecimal Secrets:</strong> ASCII conversion from hex unlocks fragments</li>
                <li><strong>🎭 Steganography:</strong> Hidden patterns in morse code, base64, and ROT13</li>
                <li><strong>🌐 Double Encoding:</strong> HTML entities encoded twice bypass filters</li>
                <li><strong>🧩 Fragment Assembly:</strong> Collect all 4 pieces to reveal the final flag</li>
            </ul>
            <div style="background: rgba(255, 0, 0, 0.1); border-left: 4px solid #ff0000; padding: 10px; margin-top: 15px; border-radius: 5px;">
                <strong>⚠️ Expert Challenge:</strong> This is not just an XSS bypass - it's a multi-disciplinary hacking challenge requiring skills in web security, cryptography, mathematics, and logical reasoning!
            </div>
        </div>
    </div>

    <!-- Load anti-cheat first to ensure it's available -->
    <script src="anti-cheat.js"></script>
    <script src="security-filter.js"></script>
    <script src="challenge.js"></script>

    <!-- Initialization and Error Handling Script -->
    <script>
        // Ensure all systems are initialized properly
        window.addEventListener('load', function() {
            console.log('Page fully loaded, checking systems...');

            // Check if anti-cheat system is available
            if (typeof window.antiCheat === 'undefined') {
                console.log('Creating fallback anti-cheat system...');
                window.antiCheat = {
                    recordViolation: function(msg) {
                        console.log('Security Violation:', msg);
                    },
                    violations: 0,
                    showWarning: function(msg) {
                        console.log('Security Warning:', msg);
                        alert('🚫 ' + msg);
                    }
                };
            }

            // Check if XSS filter is available
            if (typeof window.xssFilter === 'undefined') {
                console.log('XSS Filter not found, this may cause issues...');
            }

            // Ensure challenge functions are available
            if (typeof testPayload === 'undefined') {
                console.log('Challenge functions not loaded properly...');
            }

            console.log('System check complete:');
            console.log('- Anti-cheat:', typeof window.antiCheat !== 'undefined' ? '✅' : '❌');
            console.log('- XSS Filter:', typeof window.xssFilter !== 'undefined' ? '✅' : '❌');
            console.log('- Challenge:', typeof testPayload !== 'undefined' ? '✅' : '❌');
        });

        // Auto-retry mechanism for failed initializations
        let initRetries = 0;
        const maxRetries = 3;

        function checkAndRetryInit() {
            if (typeof window.antiCheat === 'undefined' && initRetries < maxRetries) {
                initRetries++;
                console.log(`Retry ${initRetries}/${maxRetries}: Attempting to initialize anti-cheat...`);

                setTimeout(() => {
                    if (typeof AntiCheatSystem !== 'undefined') {
                        window.antiCheat = new AntiCheatSystem();
                        console.log('Anti-cheat system initialized on retry', initRetries);
                    } else {
                        checkAndRetryInit();
                    }
                }, 1000);
            }
        }

        // Start retry mechanism after a short delay
        setTimeout(checkAndRetryInit, 500);
    </script>

    <!-- Enhanced Anti-Cheat Protection System -->
    <script>
        // Multiple layers of right-click protection

        // Method 1: Standard contextmenu event blocking
        document.addEventListener('contextmenu', function(e) {
            e.preventDefault();
            e.stopPropagation();
            e.stopImmediatePropagate();
            showSecurityWarning('Right-click disabled for security purposes!');
            return false;
        }, true);

        // Method 2: Mouse button detection
        document.addEventListener('mousedown', function(e) {
            if (e.button === 2) { // Right mouse button
                e.preventDefault();
                e.stopPropagation();
                showSecurityWarning('Right-click detected and blocked!');
                return false;
            }
        }, true);

        // Method 3: Mouse up detection for right button
        document.addEventListener('mouseup', function(e) {
            if (e.button === 2) { // Right mouse button
                e.preventDefault();
                e.stopPropagation();
                showSecurityWarning('Right-click action blocked!');
                return false;
            }
        }, true);

        // Method 4: Additional contextmenu blocking with capture
        window.addEventListener('contextmenu', function(e) {
            e.preventDefault();
            e.stopPropagation();
            return false;
        }, true);

        // Method 5: Body-level right-click blocking
        document.addEventListener('DOMContentLoaded', function() {
            document.body.addEventListener('contextmenu', function(e) {
                e.preventDefault();
                showSecurityWarning('Context menu access denied!');
                return false;
            }, true);

            // Additional mouse event blocking
            document.body.addEventListener('mousedown', function(e) {
                if (e.button === 2) {
                    e.preventDefault();
                    e.stopPropagation();
                    return false;
                }
            }, true);
        });

        // Disable F12, Ctrl+Shift+I, Ctrl+Shift+J, Ctrl+U
        document.addEventListener('keydown', function(e) {
            // F12 - Developer Tools
            if (e.keyCode === 123) {
                e.preventDefault();
                showSecurityWarning('Developer Tools access blocked!');
                return false;
            }

            // Ctrl+Shift+I - Inspector
            if (e.ctrlKey && e.shiftKey && e.keyCode === 73) {
                e.preventDefault();
                showSecurityWarning('Inspector access blocked!');
                return false;
            }

            // Ctrl+Shift+J - Console
            if (e.ctrlKey && e.shiftKey && e.keyCode === 74) {
                e.preventDefault();
                showSecurityWarning('Console access blocked!');
                return false;
            }

            // Ctrl+U - View Source
            if (e.ctrlKey && e.keyCode === 85) {
                e.preventDefault();
                showSecurityWarning('View Source blocked!');
                return false;
            }

            // Ctrl+Shift+C - Element Selector
            if (e.ctrlKey && e.shiftKey && e.keyCode === 67) {
                e.preventDefault();
                showSecurityWarning('Element selector blocked!');
                return false;
            }

            // Ctrl+Shift+K - Console (Firefox)
            if (e.ctrlKey && e.shiftKey && e.keyCode === 75) {
                e.preventDefault();
                showSecurityWarning('Console access blocked!');
                return false;
            }
        });

        // Disable text selection to prevent easy copying
        document.addEventListener('selectstart', function(e) {
            if (e.target.tagName !== 'INPUT' && e.target.tagName !== 'TEXTAREA') {
                e.preventDefault();
                return false;
            }
        });

        // Disable drag and drop
        document.addEventListener('dragstart', function(e) {
            e.preventDefault();
            return false;
        });

        // Detect developer tools opening (basic detection)
        let devtools = {
            open: false,
            orientation: null
        };

        const threshold = 160;
        setInterval(function() {
            if (window.outerHeight - window.innerHeight > threshold ||
                window.outerWidth - window.innerWidth > threshold) {
                if (!devtools.open) {
                    devtools.open = true;
                    showSecurityWarning('Developer Tools detected! Challenge integrity may be compromised.');
                    // Optional: Redirect or disable challenge
                    // window.location.href = 'about:blank';
                }
            } else {
                devtools.open = false;
            }
        }, 500);

        // Disable printing
        window.addEventListener('beforeprint', function(e) {
            e.preventDefault();
            showSecurityWarning('Printing disabled for security purposes!');
            return false;
        });

        // Clear console periodically
        setInterval(function() {
            console.clear();
            console.log('%c🐺 WOLF CTF - Anti-Cheat System Active', 'color: red; font-size: 20px; font-weight: bold;');
            console.log('%cAttempting to view source code or inspect elements is against the rules!', 'color: orange; font-size: 14px;');
            console.log('%cSolve the challenge legitimately by crafting XSS payloads!', 'color: yellow; font-size: 12px;');
        }, 3000);

        // Show security warning message
        function showSecurityWarning(message) {
            // Create warning overlay
            const warningDiv = document.createElement('div');
            warningDiv.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: linear-gradient(45deg, #ff0000, #cc0000);
                color: white;
                padding: 20px 30px;
                border-radius: 10px;
                box-shadow: 0 10px 30px rgba(255, 0, 0, 0.5);
                z-index: 10000;
                font-family: 'Courier New', monospace;
                font-weight: bold;
                text-align: center;
                border: 2px solid #ffffff;
                animation: shake 0.5s ease-in-out;
            `;

            warningDiv.innerHTML = `
                <h3>🚨 SECURITY VIOLATION DETECTED 🚨</h3>
                <p>${message}</p>
                <p style="font-size: 12px; margin-top: 15px; opacity: 0.8;">
                    Solve the challenge legitimately!<br>
                    This warning will disappear in 3 seconds.
                </p>
            `;

            document.body.appendChild(warningDiv);

            // Remove warning after 3 seconds
            setTimeout(() => {
                if (warningDiv.parentNode) {
                    warningDiv.parentNode.removeChild(warningDiv);
                }
            }, 3000);
        }

        // Add shake animation
        const shakeStyle = document.createElement('style');
        shakeStyle.textContent = `
            @keyframes shake {
                0%, 100% { transform: translate(-50%, -50%) translateX(0); }
                25% { transform: translate(-50%, -50%) translateX(-5px); }
                75% { transform: translate(-50%, -50%) translateX(5px); }
            }
        `;
        document.head.appendChild(shakeStyle);

        // Obfuscate source code in memory (basic protection)
        setTimeout(function() {
            // Remove script tags from DOM to make source viewing harder
            const scripts = document.querySelectorAll('script');
            scripts.forEach(script => {
                if (script.src) {
                    // Keep external scripts but hide inline ones
                    return;
                }
                // Comment out inline scripts
                script.textContent = '// Script content hidden for security';
            });
        }, 1000);

        // Disable common debugging methods
        window.console.log = function() {};
        window.console.info = function() {};
        window.console.warn = function() {};
        window.console.error = function() {};
        window.console.debug = function() {};

        // Override console after a delay to allow our own messages
        setTimeout(function() {
            const originalLog = console.log;
            console.log = function(...args) {
                // Only allow our security messages
                if (args[0] && args[0].includes && args[0].includes('WOLF CTF')) {
                    originalLog.apply(console, args);
                }
            };
        }, 100);

        // Detect and block common cheat attempts
        Object.defineProperty(window, 'xssFilter', {
            get: function() {
                showSecurityWarning('Direct access to filter object blocked!');
                return undefined;
            },
            set: function() {
                showSecurityWarning('Modification of filter object blocked!');
            }
        });

        // Block common inspection methods
        document.addEventListener('DOMContentLoaded', function() {
            // Disable image dragging
            const images = document.querySelectorAll('img');
            images.forEach(img => {
                img.addEventListener('dragstart', function(e) {
                    e.preventDefault();
                });
            });

            // Add additional protection message
            console.log('%c⚠️ CHALLENGE INTEGRITY PROTECTION ACTIVE ⚠️', 'color: red; font-size: 16px; font-weight: bold; background: yellow;');
        });
    </script>
</body>
</html>
