<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XSS Payload Challenge - WOLF CTF</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            color: #fff;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #ff6b6b;
            padding-bottom: 20px;
        }
        
        .challenge-info {
            background: rgba(255, 107, 107, 0.1);
            border-left: 4px solid #ff6b6b;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        
        .input-section {
            background: rgba(255, 255, 255, 0.05);
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .payload-input {
            width: 100%;
            height: 100px;
            background: #1a1a1a;
            color: #00ff00;
            border: 2px solid #333;
            border-radius: 5px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            resize: vertical;
        }
        
        .payload-input:focus {
            outline: none;
            border-color: #ff6b6b;
            box-shadow: 0 0 10px rgba(255, 107, 107, 0.3);
        }
        
        .submit-btn {
            background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            margin-top: 10px;
        }
        
        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
        }
        
        .output-section {
            background: rgba(255, 255, 255, 0.05);
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
            min-height: 100px;
        }
        
        .filter-info {
            background: rgba(255, 193, 7, 0.1);
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        
        .hint-section {
            background: rgba(40, 167, 69, 0.1);
            border-left: 4px solid #28a745;
            padding: 15px;
            margin-top: 20px;
            border-radius: 5px;
        }
        
        .security-status {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: bold;
            margin-left: 10px;
        }
        
        .secure {
            background: #28a745;
            color: white;
        }
        
        .bypassed {
            background: #dc3545;
            color: white;
            animation: blink 1s infinite;
        }
        
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.5; }
        }
        
        .code {
            background: #1a1a1a;
            color: #00ff00;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        
        .warning {
            color: #ffc107;
            font-weight: bold;
        }
        
        .success {
            color: #28a745;
            font-weight: bold;
        }
        
        .error {
            color: #dc3545;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🐺 WOLF CTF - XSS Payload Challenge</h1>
            <h2>Problem 3: Advanced XSS Bypass</h2>
            <div class="challenge-info">
                <strong>Difficulty:</strong> Hard | <strong>Points:</strong> 50 | <strong>Flag Format:</strong> WOLF{...}
            </div>
        </div>
        
        <div class="filter-info">
            <h3>🛡️ Security Filter Status</h3>
            <p>Advanced XSS Protection: <span class="security-status secure" id="security-status">ACTIVE</span></p>
            <p><strong>Filter Details:</strong> Multi-layer protection including HTML entity filtering, script tag blocking, event handler detection, and Unicode normalization.</p>
        </div>
        
        <div class="input-section">
            <h3>💉 Payload Testing Ground</h3>
            <p>Enter your XSS payload below. The system will process it through our advanced security filters.</p>
            <textarea class="payload-input" id="payload-input" placeholder="Enter your XSS payload here...
Example: <script>alert('XSS')</script>"></textarea>
            <br>
            <button class="submit-btn" onclick="testPayload()">🚀 Test Payload</button>
            <button class="submit-btn" onclick="clearOutput()" style="background: linear-gradient(45deg, #6c757d, #868e96);">🗑️ Clear</button>
        </div>
        
        <div class="output-section">
            <h3>📊 Filter Analysis Results</h3>
            <div id="filter-results">
                <p><em>No payload tested yet. Enter a payload above and click "Test Payload" to see the filtering results.</em></p>
            </div>
        </div>
        
        <div class="hint-section">
            <h3>💡 Hints</h3>
            <ul>
                <li><strong>HTML Encoding:</strong> Sometimes the way characters are encoded can bypass filters</li>
                <li><strong>Unicode Normalization:</strong> Different Unicode representations of the same character might behave differently</li>
                <li><strong>Context Matters:</strong> Where your payload gets inserted affects what will work</li>
                <li><strong>Nested Encoding:</strong> Multiple layers of encoding might confuse the filter</li>
                <li><strong>Alternative Vectors:</strong> Not all XSS requires <span class="code">&lt;script&gt;</span> tags</li>
            </ul>
        </div>
    </div>

    <script src="security-filter.js"></script>
    <script src="challenge.js"></script>
</body>
</html>
