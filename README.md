# 🐺 WOLF CTF - XSS Payload Challenge

## Problem 3: Advanced XSS Bypass

**Difficulty:** Hard  
**Points:** 50  
**Flag:** `WOLF{xss_byp4ss_3xp3rt}`

### 📋 Challenge Description

Welcome to the advanced XSS payload challenge! Your mission is to craft an XSS payload that can bypass our sophisticated multi-layer security filter system. This challenge tests your understanding of HTML encoding, Unicode normalization, and advanced XSS bypass techniques.

The security system implements multiple layers of protection:
- Basic script tag filtering
- Event handler detection
- HTML entity filtering
- Unicode normalization
- Contextual filtering
- Advanced pattern matching

However, like all security systems, it has vulnerabilities that can be exploited by a skilled attacker.

### 🎯 Objective

Craft an XSS payload that:
1. Bypasses all security filter layers
2. Successfully executes JavaScript code
3. Reveals the hidden flag

### 💡 Hints

- **HTML Encoding is Key:** The vulnerability lies in how HTML entities are processed
- **Double Encoding:** Sometimes encoding your encoding can confuse filters
- **Context Matters:** Understanding where your payload gets processed is crucial
- **Think Like a Filter:** What would a security filter miss?

### 🚀 Getting Started

1. Open `index.html` in your web browser
2. Enter your XSS payload in the testing area
3. Click "Test Payload" to see how the security filters respond
4. Analyze the filter results and adjust your approach
5. Keep trying until you successfully bypass all filters!

### 🔧 Technical Details

#### Security Filter Layers

1. **Basic Script Filter**
   - Blocks `<script>` tags using regex patterns
   - Replaces detected scripts with `[BLOCKED_SCRIPT]`

2. **Event Handler Filter**
   - Detects and blocks event handlers like `onclick`, `onload`, etc.
   - Uses pattern matching to identify dangerous attributes

3. **HTML Entity Filter**
   - Decodes HTML entities like `&lt;`, `&gt;`, `&quot;`
   - **Vulnerability:** Doesn't handle double-encoded entities properly
   - This is where the main bypass opportunity exists

4. **Unicode Normalization**
   - Normalizes Unicode characters to prevent Unicode-based bypasses
   - Blocks suspicious Unicode control characters

5. **Contextual Filter**
   - Blocks dangerous URL schemes like `javascript:` and `vbscript:`
   - Context-aware filtering based on placement

6. **Advanced Pattern Filter**
   - Blocks sophisticated attack vectors
   - Detects patterns like `data:text/html`, `expression()`, etc.

#### The Intended Solution

The main vulnerability is in the HTML entity filter. It performs single-pass decoding, which means double-encoded entities can bypass the filter:

**Normal payload (blocked):**
```html
<script>alert('XSS')</script>
```

**Single encoded (blocked):**
```html
&lt;script&gt;alert('XSS')&lt;/script&gt;
```

**Double encoded (bypasses filter):**
```html
&amp;lt;script&amp;gt;alert('XSS')&amp;lt;/script&amp;gt;
```

The filter decodes `&amp;` to `&`, resulting in `&lt;script&gt;alert('XSS')&lt;/script&gt;`, which then gets decoded again in the browser context, executing the script.

### 🏆 Success Criteria

Your payload is successful when:
- All security filters are bypassed
- The security status changes to "BYPASSED"
- The flag `WOLF{xss_byp4ss_3xp3rt}` is revealed
- A success message appears confirming the bypass

### 📁 File Structure

```
capfile/
├── index.html          # Main challenge interface
├── security-filter.js  # Advanced XSS filter implementation
├── challenge.js        # Challenge interface controller
├── README.md          # This documentation
├── SOLUTION.md        # Detailed solution guide
└── test-payloads.txt  # Example payloads for testing
```

### 🧪 Testing

The challenge includes built-in testing capabilities:
- Real-time payload analysis
- Filter layer breakdown
- Bypass attempt detection
- Success/failure feedback
- Attempt counter and timing

### 🔒 Security Notes

This challenge is designed for educational purposes to demonstrate:
- Common XSS filter bypass techniques
- The importance of proper input validation
- How encoding/decoding vulnerabilities work
- Defense-in-depth security principles

**Important:** Never use these techniques on systems you don't own or without explicit permission.

### 🎓 Learning Objectives

After completing this challenge, you should understand:
- How HTML entity encoding works
- Why double encoding can bypass filters
- The importance of proper input sanitization
- How to think like both an attacker and defender
- Advanced XSS bypass techniques

### 🏁 Next Steps

Once you've successfully completed this challenge:
1. Try to find alternative bypass methods
2. Analyze the filter code to understand other potential vulnerabilities
3. Consider how you would improve the security filter
4. Explore other XSS vectors and bypass techniques

Good luck, and happy hacking! 🐺

---

**Flag Format:** `WOLF{...}`  
**Challenge Author:** WOLF CTF Team  
**Difficulty Rating:** ⭐⭐⭐⭐⭐ (5/5)
