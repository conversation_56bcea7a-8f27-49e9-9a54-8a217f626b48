/**
 * XSS Challenge Interface Controller
 * WOLF CTF - Problem 3: XSS Payload Challenge
 */

// Challenge state management
let challengeState = {
    attempts: 0,
    successfulBypasses: 0,
    flagRevealed: false,
    startTime: Date.now()
};

/**
 * Test the user's XSS payload against the security filter
 */
function testPayload() {
    const payloadInput = document.getElementById('payload-input');
    const resultsDiv = document.getElementById('filter-results');
    const payload = payloadInput.value.trim();
    
    if (!payload) {
        showError('Please enter a payload to test.');
        return;
    }
    
    challengeState.attempts++;
    
    // Clear previous results
    resultsDiv.innerHTML = '<p><em>Processing payload through security filters...</em></p>';
    
    // Simulate processing delay for realism
    setTimeout(() => {
        // Run the payload through the security filter
        const filterResult = window.xssFilter.filterInput(payload);
        displayFilterResults(filterResult);
        
        // Log attempt for analytics
        logAttempt(payload, filterResult);
    }, 500);
}

/**
 * Display the results of the security filter analysis
 */
function displayFilterResults(result) {
    const resultsDiv = document.getElementById('filter-results');
    
    let html = `
        <div class="filter-analysis">
            <h4>🔍 Security Analysis Report</h4>
            <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 5px; margin: 10px 0;">
                <strong>Original Payload:</strong><br>
                <code style="background: #1a1a1a; color: #00ff00; padding: 5px; border-radius: 3px; display: block; margin: 5px 0; word-break: break-all;">${escapeHtml(result.original)}</code>
            </div>
            
            <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 5px; margin: 10px 0;">
                <strong>Filtered Output:</strong><br>
                <code style="background: #1a1a1a; color: #ff6b6b; padding: 5px; border-radius: 3px; display: block; margin: 5px 0; word-break: break-all;">${escapeHtml(result.filtered)}</code>
            </div>
            
            <div class="filter-status" style="margin: 15px 0;">
                <strong>Security Status:</strong> 
                <span class="status-badge ${result.blocked ? 'blocked' : 'passed'}" style="padding: 3px 8px; border-radius: 12px; font-size: 12px; font-weight: bold;">
                    ${result.blocked ? '🛡️ BLOCKED' : '✅ PASSED'}
                </span>
                <span class="security-level" style="margin-left: 10px; padding: 3px 8px; border-radius: 12px; font-size: 12px; font-weight: bold; background: ${result.securityLevel === 'COMPROMISED' ? '#dc3545' : '#28a745'}; color: white;">
                    ${result.securityLevel}
                </span>
            </div>
    `;
    
    // Show filter layers analysis
    html += `
        <div class="filter-layers" style="margin: 15px 0;">
            <strong>Filter Layers Analysis:</strong>
            <div style="margin: 10px 0;">
    `;
    
    result.filtersPassed.forEach(filter => {
        html += `<span class="filter-passed" style="display: inline-block; margin: 2px; padding: 2px 6px; background: #28a745; color: white; border-radius: 10px; font-size: 11px;">✓ ${formatFilterName(filter)}</span>`;
    });
    
    result.filtersBlocked.forEach(filter => {
        html += `<span class="filter-blocked" style="display: inline-block; margin: 2px; padding: 2px 6px; background: #dc3545; color: white; border-radius: 10px; font-size: 11px;">✗ ${formatFilterName(filter)}</span>`;
    });
    
    html += `</div></div>`;
    
    // Show warnings if any
    if (result.warnings.length > 0) {
        html += `
            <div class="warnings" style="background: rgba(255, 193, 7, 0.1); border-left: 3px solid #ffc107; padding: 10px; margin: 10px 0;">
                <strong>⚠️ Security Warnings:</strong>
                <ul style="margin: 5px 0; padding-left: 20px;">
        `;
        result.warnings.forEach(warning => {
            html += `<li>${warning}</li>`;
        });
        html += `</ul></div>`;
    }
    
    // Show bypass detection
    if (result.bypassAttempted) {
        html += `
            <div class="bypass-detection" style="background: rgba(255, 107, 107, 0.1); border-left: 3px solid #ff6b6b; padding: 10px; margin: 10px 0;">
                <strong>🚨 Bypass Attempt Detected!</strong><br>
                <em>The system detected that you're attempting to bypass the security filters.</em>
        `;
        
        if (result.bypassMethod) {
            html += `<br><strong>Method:</strong> ${result.bypassMethod}`;
        }
        
        html += `</div>`;
    }
    
    // Show success message if flag is revealed
    if (result.flagRevealed) {
        challengeState.successfulBypasses++;
        challengeState.flagRevealed = true;
        
        html += `
            <div class="success-message" style="background: linear-gradient(45deg, #28a745, #20c997); color: white; padding: 20px; border-radius: 10px; margin: 15px 0; text-align: center; box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);">
                <h3>🎉 CONGRATULATIONS! 🎉</h3>
                <p><strong>You have successfully bypassed the advanced XSS filter!</strong></p>
                <p><strong>Flag:</strong> <span style="background: rgba(0,0,0,0.3); padding: 8px 15px; border-radius: 5px; font-family: 'Courier New', monospace; font-size: 18px; letter-spacing: 1px;">${result.flag}</span></p>
                <p><em>Method: Double HTML Entity Encoding Bypass</em></p>
                <div style="margin-top: 15px; font-size: 14px; opacity: 0.9;">
                    <p>🏆 Challenge completed in ${challengeState.attempts} attempts</p>
                    <p>⏱️ Time taken: ${Math.round((Date.now() - challengeState.startTime) / 1000)} seconds</p>
                </div>
            </div>
        `;
    }
    
    html += `
        <div class="attempt-counter" style="text-align: center; margin-top: 15px; opacity: 0.7; font-size: 12px;">
            Attempt #${challengeState.attempts} | Successful bypasses: ${challengeState.successfulBypasses}
        </div>
    </div>`;
    
    resultsDiv.innerHTML = html;
    
    // Scroll to results
    resultsDiv.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
}

/**
 * Clear the output section
 */
function clearOutput() {
    const resultsDiv = document.getElementById('filter-results');
    const payloadInput = document.getElementById('payload-input');
    
    resultsDiv.innerHTML = '<p><em>Output cleared. Enter a new payload to test.</em></p>';
    payloadInput.value = '';
    payloadInput.focus();
}

/**
 * Format filter names for display
 */
function formatFilterName(filterName) {
    const names = {
        'basicScriptFilter': 'Script Tag Filter',
        'eventHandlerFilter': 'Event Handler Filter',
        'htmlEntityFilter': 'HTML Entity Filter',
        'unicodeNormalization': 'Unicode Normalization',
        'contextualFilter': 'Contextual Filter',
        'advancedPatternFilter': 'Advanced Pattern Filter'
    };
    return names[filterName] || filterName;
}

/**
 * Escape HTML for safe display
 */
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

/**
 * Show error message
 */
function showError(message) {
    const resultsDiv = document.getElementById('filter-results');
    resultsDiv.innerHTML = `
        <div style="background: rgba(220, 53, 69, 0.1); border-left: 3px solid #dc3545; padding: 15px; color: #dc3545;">
            <strong>❌ Error:</strong> ${message}
        </div>
    `;
}

/**
 * Log attempt for analytics (in a real scenario, this would send to a server)
 */
function logAttempt(payload, result) {
    const logEntry = {
        timestamp: new Date().toISOString(),
        attempt: challengeState.attempts,
        payload: payload,
        blocked: result.blocked,
        bypassAttempted: result.bypassAttempted,
        bypassMethod: result.bypassMethod,
        flagRevealed: result.flagRevealed
    };
    
    // Store in localStorage for demo purposes
    let logs = JSON.parse(localStorage.getItem('xss-challenge-logs') || '[]');
    logs.push(logEntry);
    localStorage.setItem('xss-challenge-logs', JSON.stringify(logs));
    
    console.log('Challenge attempt logged:', logEntry);
}

/**
 * Initialize the challenge interface
 */
function initializeChallenge() {
    // Add keyboard shortcuts
    document.getElementById('payload-input').addEventListener('keydown', function(e) {
        if (e.ctrlKey && e.key === 'Enter') {
            testPayload();
        }
    });
    
    // Add some example payloads for testing
    const examples = [
        '<script>alert("XSS")</script>',
        '<img src=x onerror=alert("XSS")>',
        'javascript:alert("XSS")',
        '&lt;script&gt;alert("XSS")&lt;/script&gt;',
        '&amp;lt;script&amp;gt;alert("XSS")&amp;lt;/script&amp;gt;'
    ];
    
    // Store examples for potential use
    window.examplePayloads = examples;
    
    console.log('XSS Challenge initialized');
    console.log('Hint: Try double HTML entity encoding...');
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', initializeChallenge);

// Add CSS for dynamic elements
const style = document.createElement('style');
style.textContent = `
    .status-badge.blocked {
        background: #dc3545;
        color: white;
    }
    
    .status-badge.passed {
        background: #28a745;
        color: white;
    }
    
    .filter-analysis {
        animation: fadeIn 0.5s ease-in;
    }
    
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }
`;
document.head.appendChild(style);
