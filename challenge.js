/**
 * XSS Challenge Interface Controller
 * WOLF CTF - Problem 3: XSS Payload Challenge
 * Protected by Anti-Cheat System
 */

// Challenge state management (protected)
let challengeState = {
    attempts: 0,
    successfulBypasses: 0,
    flagRevealed: false,
    startTime: Date.now(),
    integrity: true
};

// Anti-tampering protection
Object.defineProperty(challengeState, 'integrity', {
    writable: false,
    configurable: false
});

// Protect challenge functions from modification
const originalTestPayload = testPayload;
const originalClearOutput = clearOutput;

/**
 * Test the user's XSS payload against the security filter
 */
function testPayload() {
    // Anti-tampering check
    if (!challengeState.integrity) {
        showError('Challenge integrity compromised. Please refresh the page.');
        return;
    }

    // Check for anti-cheat system
    if (typeof window.antiCheat === 'undefined') {
        showError('Security system not initialized. Please refresh the page.');
        return;
    }

    const payloadInput = document.getElementById('payload-input');
    const resultsDiv = document.getElementById('filter-results');
    const payload = payloadInput.value.trim();

    if (!payload) {
        showError('Please enter a payload to test.');
        return;
    }

    // Check for suspicious payload patterns that might indicate cheating
    if (payload.includes('xssFilter') || payload.includes('challengeState') || payload.includes('antiCheat')) {
        window.antiCheat && window.antiCheat.recordViolation('Suspicious payload content detected');
        showError('Suspicious payload detected. Focus on XSS techniques, not system manipulation.');
        return;
    }

    challengeState.attempts++;

    // Clear previous results
    resultsDiv.innerHTML = '<p><em>Processing payload through security filters...</em></p>';

    // Simulate processing delay for realism
    setTimeout(() => {
        try {
            // Run the payload through the security filter
            const filterResult = window.xssFilter.filterInput(payload);
            displayFilterResults(filterResult);

            // Log attempt for analytics
            logAttempt(payload, filterResult);
        } catch (error) {
            showError('An error occurred while processing your payload. Please try again.');
            console.error('Filter error:', error);
        }
    }, 500);
}

/**
 * Display the results of the security filter analysis
 */
function displayFilterResults(result) {
    const resultsDiv = document.getElementById('filter-results');

    let html = `
        <div class="filter-analysis">
            <h4>🔍 Advanced Security Analysis Report</h4>
            <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 5px; margin: 10px 0;">
                <strong>Original Payload:</strong><br>
                <code style="background: #1a1a1a; color: #00ff00; padding: 5px; border-radius: 3px; display: block; margin: 5px 0; word-break: break-all;">${escapeHtml(result.original)}</code>
            </div>

            <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 5px; margin: 10px 0;">
                <strong>Filtered Output:</strong><br>
                <code style="background: #1a1a1a; color: #ff6b6b; padding: 5px; border-radius: 3px; display: block; margin: 5px 0; word-break: break-all;">${escapeHtml(result.filtered)}</code>
            </div>

            <div class="filter-status" style="margin: 15px 0;">
                <strong>Security Status:</strong>
                <span class="status-badge ${result.blocked ? 'blocked' : 'passed'}" style="padding: 3px 8px; border-radius: 12px; font-size: 12px; font-weight: bold;">
                    ${result.blocked ? '🛡️ BLOCKED' : '✅ PASSED'}
                </span>
                <span class="security-level" style="margin-left: 10px; padding: 3px 8px; border-radius: 12px; font-size: 12px; font-weight: bold; background: ${result.securityLevel === 'COMPROMISED' ? '#dc3545' : '#28a745'}; color: white;">
                    ${result.securityLevel}
                </span>
            </div>
    `;

    // Show hidden challenges detected
    if (result.hiddenChallenges && result.hiddenChallenges.length > 0) {
        html += `
            <div class="hidden-challenges" style="background: rgba(138, 43, 226, 0.1); border-left: 3px solid #8a2be2; padding: 15px; margin: 10px 0; border-radius: 5px;">
                <h4>🧩 Hidden Logic Challenges Detected!</h4>
        `;
        result.hiddenChallenges.forEach(challenge => {
            html += `
                <div style="margin: 8px 0; padding: 8px; background: rgba(255,255,255,0.05); border-radius: 3px;">
                    <strong>🧠 ${challenge.type.replace('_', ' ').toUpperCase()}:</strong><br>
                    <em>Fragment "${challenge.fragment}" unlocked!</em>
                </div>
            `;
        });
        html += `</div>`;
    }

    // Show steganography clues
    if (result.steganographyClues && result.steganographyClues.length > 0) {
        html += `
            <div class="steganography-clues" style="background: rgba(255, 140, 0, 0.1); border-left: 3px solid #ff8c00; padding: 15px; margin: 10px 0; border-radius: 5px;">
                <h4>🔍 Steganographic Patterns Detected!</h4>
        `;
        result.steganographyClues.forEach(clue => {
            html += `
                <div style="margin: 8px 0; padding: 8px; background: rgba(255,255,255,0.05); border-radius: 3px;">
                    <strong>📡 ${clue.type.replace('_', ' ').toUpperCase()}:</strong> ${clue.hint}<br>
                    ${clue.decoded ? `<em>Decoded: ${clue.decoded}</em>` : ''}
                </div>
            `;
        });
        html += `</div>`;
    }

    // Show logic puzzles solved
    if (result.logicPuzzles && result.logicPuzzles.length > 0) {
        html += `
            <div class="logic-puzzles" style="background: rgba(0, 255, 127, 0.1); border-left: 3px solid #00ff7f; padding: 15px; margin: 10px 0; border-radius: 5px;">
                <h4>🧠 Logic Puzzles Solved!</h4>
        `;
        result.logicPuzzles.forEach(puzzle => {
            html += `
                <div style="margin: 8px 0; padding: 8px; background: rgba(255,255,255,0.05); border-radius: 3px;">
                    <strong>🎯 ${puzzle.type.replace('_', ' ').toUpperCase()}:</strong> ${puzzle.solution}<br>
                    <em>Fragment "${puzzle.fragment}" acquired!</em>
                </div>
            `;
        });
        html += `</div>`;
    }

    // Show partial success with hints
    if (result.partialSuccess) {
        html += `
            <div class="partial-success" style="background: rgba(255, 193, 7, 0.1); border-left: 3px solid #ffc107; padding: 15px; margin: 10px 0; border-radius: 5px;">
                <h4>⚡ Partial Bypass Achieved!</h4>
                <p><strong>Progress:</strong> ${4 - result.fragmentsNeeded}/4 flag fragments unlocked</p>
                <p><strong>Remaining:</strong> ${result.fragmentsNeeded} more fragments needed</p>
                <p><strong>💡 Next Hint:</strong> <em>${result.hint}</em></p>
                <div style="margin-top: 10px; font-size: 12px; opacity: 0.8;">
                    <p>🔍 Try embedding mathematical sequences, binary patterns, or cryptographic elements in your payload!</p>
                </div>
            </div>
        `;
    }
    
    // Show filter layers analysis
    html += `
        <div class="filter-layers" style="margin: 15px 0;">
            <strong>Filter Layers Analysis:</strong>
            <div style="margin: 10px 0;">
    `;
    
    result.filtersPassed.forEach(filter => {
        html += `<span class="filter-passed" style="display: inline-block; margin: 2px; padding: 2px 6px; background: #28a745; color: white; border-radius: 10px; font-size: 11px;">✓ ${formatFilterName(filter)}</span>`;
    });
    
    result.filtersBlocked.forEach(filter => {
        html += `<span class="filter-blocked" style="display: inline-block; margin: 2px; padding: 2px 6px; background: #dc3545; color: white; border-radius: 10px; font-size: 11px;">✗ ${formatFilterName(filter)}</span>`;
    });
    
    html += `</div></div>`;
    
    // Show warnings if any
    if (result.warnings.length > 0) {
        html += `
            <div class="warnings" style="background: rgba(255, 193, 7, 0.1); border-left: 3px solid #ffc107; padding: 10px; margin: 10px 0;">
                <strong>⚠️ Security Warnings:</strong>
                <ul style="margin: 5px 0; padding-left: 20px;">
        `;
        result.warnings.forEach(warning => {
            html += `<li>${warning}</li>`;
        });
        html += `</ul></div>`;
    }
    
    // Show bypass detection
    if (result.bypassAttempted) {
        html += `
            <div class="bypass-detection" style="background: rgba(255, 107, 107, 0.1); border-left: 3px solid #ff6b6b; padding: 10px; margin: 10px 0;">
                <strong>🚨 Bypass Attempt Detected!</strong><br>
                <em>The system detected that you're attempting to bypass the security filters.</em>
        `;
        
        if (result.bypassMethod) {
            html += `<br><strong>Method:</strong> ${result.bypassMethod}`;
        }
        
        html += `</div>`;
    }
    
    // Show success message if flag is revealed
    if (result.flagRevealed) {
        challengeState.successfulBypasses++;
        challengeState.flagRevealed = true;
        
        html += `
            <div class="success-message" style="background: linear-gradient(45deg, #28a745, #20c997); color: white; padding: 20px; border-radius: 10px; margin: 15px 0; text-align: center; box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);">
                <h3>🎉 CONGRATULATIONS! 🎉</h3>
                <p><strong>You have successfully bypassed the advanced XSS filter!</strong></p>
                <p><strong>Flag:</strong> <span style="background: rgba(0,0,0,0.3); padding: 8px 15px; border-radius: 5px; font-family: 'Courier New', monospace; font-size: 18px; letter-spacing: 1px;">${result.flag}</span></p>
                <p><em>Method: Double HTML Entity Encoding Bypass</em></p>
                <div style="margin-top: 15px; font-size: 14px; opacity: 0.9;">
                    <p>🏆 Challenge completed in ${challengeState.attempts} attempts</p>
                    <p>⏱️ Time taken: ${Math.round((Date.now() - challengeState.startTime) / 1000)} seconds</p>
                </div>
            </div>
        `;
    }
    
    html += `
        <div class="attempt-counter" style="text-align: center; margin-top: 15px; opacity: 0.7; font-size: 12px;">
            Attempt #${challengeState.attempts} | Successful bypasses: ${challengeState.successfulBypasses}
        </div>
    </div>`;
    
    resultsDiv.innerHTML = html;
    
    // Scroll to results
    resultsDiv.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
}

/**
 * Clear the output section
 */
function clearOutput() {
    const resultsDiv = document.getElementById('filter-results');
    const payloadInput = document.getElementById('payload-input');
    
    resultsDiv.innerHTML = '<p><em>Output cleared. Enter a new payload to test.</em></p>';
    payloadInput.value = '';
    payloadInput.focus();
}

/**
 * Format filter names for display
 */
function formatFilterName(filterName) {
    const names = {
        'basicScriptFilter': 'Script Tag Filter',
        'eventHandlerFilter': 'Event Handler Filter',
        'htmlEntityFilter': 'HTML Entity Filter',
        'unicodeNormalization': 'Unicode Normalization',
        'contextualFilter': 'Contextual Filter',
        'advancedPatternFilter': 'Advanced Pattern Filter'
    };
    return names[filterName] || filterName;
}

/**
 * Escape HTML for safe display
 */
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

/**
 * Show error message
 */
function showError(message) {
    const resultsDiv = document.getElementById('filter-results');
    resultsDiv.innerHTML = `
        <div style="background: rgba(220, 53, 69, 0.1); border-left: 3px solid #dc3545; padding: 15px; color: #dc3545;">
            <strong>❌ Error:</strong> ${message}
        </div>
    `;
}

/**
 * Log attempt for analytics (in a real scenario, this would send to a server)
 */
function logAttempt(payload, result) {
    const logEntry = {
        timestamp: new Date().toISOString(),
        attempt: challengeState.attempts,
        payload: payload,
        blocked: result.blocked,
        bypassAttempted: result.bypassAttempted,
        bypassMethod: result.bypassMethod,
        flagRevealed: result.flagRevealed
    };
    
    // Store in localStorage for demo purposes
    let logs = JSON.parse(localStorage.getItem('xss-challenge-logs') || '[]');
    logs.push(logEntry);
    localStorage.setItem('xss-challenge-logs', JSON.stringify(logs));
    
    console.log('Challenge attempt logged:', logEntry);
}

/**
 * Initialize the challenge interface
 */
function initializeChallenge() {
    // Add keyboard shortcuts
    document.getElementById('payload-input').addEventListener('keydown', function(e) {
        if (e.ctrlKey && e.key === 'Enter') {
            testPayload();
        }
    });
    
    // Add some example payloads for testing
    const examples = [
        '<script>alert("XSS")</script>',
        '<img src=x onerror=alert("XSS")>',
        'javascript:alert("XSS")',
        '&lt;script&gt;alert("XSS")&lt;/script&gt;',
        '&amp;lt;script&amp;gt;alert("XSS")&amp;lt;/script&amp;gt;'
    ];
    
    // Store examples for potential use
    window.examplePayloads = examples;
    
    console.log('XSS Challenge initialized');
    console.log('Hint: Try double HTML entity encoding...');
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', initializeChallenge);

// Add CSS for dynamic elements
const style = document.createElement('style');
style.textContent = `
    .status-badge.blocked {
        background: #dc3545;
        color: white;
    }
    
    .status-badge.passed {
        background: #28a745;
        color: white;
    }
    
    .filter-analysis {
        animation: fadeIn 0.5s ease-in;
    }
    
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }
`;
document.head.appendChild(style);
