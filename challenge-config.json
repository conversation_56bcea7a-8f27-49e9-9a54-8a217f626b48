{"challenge": {"id": "prob3", "title": "XSS PAYLOAD", "description": "Craft an XSS payload that bypasses the advanced filtering system. The flag is hidden in fragments across multiple logical layers.", "points": 75, "difficulty": "extreme", "flag_fragments": {"part1": "V09MRg==", "part2": "eHNzX2J5cA==", "part3": "NGFzc18z", "part4": "eHAzcnQ=", "separator": "e30=", "encoding": "base64", "assembly_logic": "fibonacci_sequence", "validation_key": "7468697320697320612073656372657420766572696669636174696f6e206b6579"}, "hidden_challenges": {"logic_puzzle_1": {"type": "mathematical_sequence", "sequence": [1, 1, 2, 3, 5, 8, 13, 21], "next_required": 34, "unlock_fragment": "part1"}, "logic_puzzle_2": {"type": "binary_conversion", "decimal_input": 120, "binary_required": "1111000", "unlock_fragment": "part2"}, "logic_puzzle_3": {"type": "caesar_cipher", "encrypted": "WKLV LV D WHVW", "shift": 3, "plaintext_required": "THIS IS A TEST", "unlock_fragment": "part3"}, "logic_puzzle_4": {"type": "hex_ascii_conversion", "hex_input": "48656c6c6f", "ascii_required": "Hello", "unlock_fragment": "part4"}}, "hint": "The flag is scattered like puzzle pieces. Each bypass reveals a fragment, but logic unlocks the truth.", "category": "web", "tags": ["xss", "bypass", "encoding", "logic-puzzles", "cryptography", "steganography"], "author": "WOLF CTF Team", "version": "2.0"}, "security": {"filterLayers": [{"name": "basicScriptFilter", "description": "Blocks basic script tags", "enabled": true, "priority": 1}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Blocks event handlers like onclick, onload", "enabled": true, "priority": 2}, {"name": "htmlEntityFilter", "description": "Decodes and filters HTML entities (VULNERABLE)", "enabled": true, "priority": 3, "vulnerability": "single-pass-decoding"}, {"name": "unicodeNormalization", "description": "Normalizes Unicode characters", "enabled": true, "priority": 4}, {"name": "contextualFilter", "description": "Context-aware filtering for URLs", "enabled": true, "priority": 5}, {"name": "advancedPatternFilter", "description": "Advanced attack pattern detection", "enabled": true, "priority": 6}]}, "validation": {"successPayloads": ["&amp;lt;script&amp;gt;alert('XSS')&amp;lt;/script&amp;gt;", "&amp;lt;script&amp;gt;alert('WOLF')&amp;lt;/script&amp;gt;", "&amp;lt;script&amp;gt;alert(\"XSS\")&amp;lt;/script&amp;gt;", "&amp;lt;img src=x onerror=alert('XSS')&amp;gt;", "&amp;lt;svg onload=alert('XSS')&amp;gt;"], "blockedPayloads": ["<script>alert('XSS')</script>", "<img src=x onerror=alert('XSS')>", "&lt;script&gt;alert('XSS')&lt;/script&gt;", "javascript:alert('XSS')", "<iframe src=javascript:alert('XSS')></iframe>"]}, "scoring": {"basePoints": 50, "timeBonus": {"under5min": 10, "under10min": 5, "under15min": 2}, "attemptPenalty": {"over10attempts": -5, "over20attempts": -10}}, "hints": [{"level": 1, "text": "HTML encoding might help - look at how entities are processed", "cost": 0}, {"level": 2, "text": "Try encoding your encoding - what happens with double HTML entities?", "cost": 5}, {"level": 3, "text": "The filter only decodes once, but browsers decode multiple times", "cost": 10}, {"level": 4, "text": "Use &amp;lt; instead of &lt; in your payload", "cost": 15}], "learning": {"objectives": ["Understand HTML entity encoding vulnerabilities", "Learn double encoding bypass techniques", "Practice XSS filter evasion", "Analyze multi-layer security systems", "Develop security testing methodology"], "concepts": ["HTML Entity Encoding", "Double Encoding Attacks", "XSS Filter Bypass", "Security Filter Analysis", "Context-Aware Security"]}, "testing": {"automated": true, "testCases": [{"name": "Basic Script Blocking", "payload": "<script>alert('test')</script>", "expectedResult": "blocked", "filterLayer": "basicScriptFilter"}, {"name": "Event Handler Blocking", "payload": "<img src=x onerror=alert('test')>", "expectedResult": "blocked", "filterLayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "Single Entity Blocking", "payload": "&lt;script&gt;alert('test')&lt;/script&gt;", "expectedResult": "blocked", "filterLayer": "htmlEntityFilter"}, {"name": "Double Entity Bypass", "payload": "&amp;lt;script&amp;gt;alert('test')&amp;lt;/script&amp;gt;", "expectedResult": "bypassed", "filterLayer": "htmlEntityFilter"}]}, "deployment": {"files": ["index.html", "security-filter.js", "challenge.js", "README.md", "SOLUTION.md", "test-payloads.txt", "challenge-config.json"], "requirements": {"browser": "Modern browser with JavaScript enabled", "server": "Static file server (optional)", "dependencies": "None"}}}